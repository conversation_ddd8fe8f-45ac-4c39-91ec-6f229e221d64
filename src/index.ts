import { Hono } from 'hono';
import { cors } from 'hono/cors';

type Env = {
  DB: D1Database;
};

const app = new Hono<{ Bindings: Env }>();

app.use('/*', cors());

// 主頁 - 行事曆介面
app.get('/', (c) => {
  return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SGS排行程</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; background: #f5f5f5; }

    /* 頂部篩選區 */
    .header { background: #2563eb; color: white; padding: 20px; }
    .header h1 { text-align: center; margin-bottom: 20px; }
    .filter-section { display: flex; gap: 15px; align-items: center; justify-content: center; flex-wrap: wrap; }
    .filter-group { display: flex; align-items: center; gap: 8px; }
    .filter-group label { font-weight: 500; }
    .filter-group select { padding: 8px 12px; border: none; border-radius: 6px; min-width: 120px; }

    /* 行事曆區域 */
    .calendar-container { max-width: 1200px; margin: 30px auto; padding: 0 20px; }
    .calendar-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
    .calendar-nav { display: flex; gap: 10px; }
    .nav-btn { background: #3b82f6; color: white; border: none; padding: 10px 15px; border-radius: 6px; cursor: pointer; }
    .nav-btn:hover { background: #2563eb; }
    .month-year { font-size: 24px; font-weight: bold; color: #1f2937; }

    /* 行事曆網格 */
    .calendar-grid { display: grid; grid-template-columns: repeat(7, 1fr); gap: 1px; background: #e5e7eb; border-radius: 8px; overflow: hidden; }
    .calendar-day-header { background: #f3f4f6; padding: 12px; text-align: center; font-weight: 600; color: #374151; }
    .calendar-day { background: white; min-height: 120px; padding: 8px; cursor: pointer; transition: all 0.2s; position: relative; }
    .calendar-day:hover { background: #f9fafb; }
    .calendar-day.other-month { background: #f9fafb; color: #9ca3af; }
    .calendar-day.today { background: #dbeafe; border: 2px solid #3b82f6; }
    .calendar-day.has-schedule { background: #fef3c7; border-left: 4px solid #f59e0b; }
    .calendar-day.filtered-region { background: #dcfce7; border-left: 4px solid #10b981; }
    .calendar-day.bookable-range { background: #dbeafe; border-left: 4px solid #3b82f6; }

    .day-number { font-weight: 600; margin-bottom: 4px; }
    .day-region { font-size: 12px; color: #059669; font-weight: 500; margin-bottom: 2px; }
    .day-title { font-size: 11px; color: #374151; line-height: 1.2; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; }

    /* 模態框 */
    .modal { display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 1000; }
    .modal.active { display: flex; }
    .modal-content { background: white; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; max-height: 80vh; overflow-y: auto; }
    .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
    .modal-header h2 { color: #1f2937; }
    .close-btn { background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; }

    .form-group { margin-bottom: 15px; }
    .form-group label { display: block; margin-bottom: 5px; color: #374151; font-weight: 500; }
    .form-group input, .form-group textarea, .form-group select { width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
    .form-group textarea { resize: vertical; min-height: 80px; }

    .schedule-items { margin-top: 20px; }
    .schedule-item { background: #f9fafb; padding: 15px; margin-bottom: 10px; border-radius: 6px; border-left: 4px solid #3b82f6; }
    .item-time { color: #3b82f6; font-weight: 600; margin-right: 10px; }
    .item-location { color: #059669; margin-right: 10px; }

    .btn-group { display: flex; gap: 10px; margin-top: 20px; }
    .btn-primary { background: #3b82f6; color: white; padding: 12px 20px; border: none; border-radius: 6px; cursor: pointer; flex: 1; }
    .btn-secondary { background: #6b7280; color: white; padding: 12px 20px; border: none; border-radius: 6px; cursor: pointer; flex: 1; }
    .btn-danger { background: #dc2626; color: white; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; }

    .add-item-btn { background: #10b981; color: white; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; margin-bottom: 15px; }

    /* Toast 提示樣式 */
    .toast {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      max-width: 400px;
      min-width: 300px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      border-left: 4px solid #3b82f6;
      animation: slideInRight 0.3s ease-out;
    }

    .toast-success {
      border-left-color: #10b981;
    }

    .toast-error {
      border-left-color: #dc2626;
    }

    .toast-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
    }

    .toast-message {
      flex: 1;
      color: #1f2937;
      font-size: 14px;
    }

    .toast-close {
      background: none;
      border: none;
      font-size: 18px;
      font-weight: bold;
      color: #6b7280;
      cursor: pointer;
      padding: 0;
      margin-left: 12px;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .toast-close:hover {
      color: #374151;
    }

    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    /* Loading 動畫樣式 */
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10001;
    }

    .loading-spinner {
      background: white;
      border-radius: 8px;
      padding: 30px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 15px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-spinner p {
      margin: 0;
      color: #374151;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🗓️ SGS排行程</h1>
    <div class="distance-section" style="margin-top: 15px; display: flex; gap: 15px; align-items: center; justify-content: center;">
      <div class="filter-group">
        <label>輸入地區查詢附近行程：</label>
        <select id="inputCity">
          <option value="">請選擇縣市</option>
        </select>
      </div>
      <div class="filter-group">
        <select id="inputDistrict">
          <option value="">請先選擇縣市</option>
        </select>
      </div>
      <button class="nav-btn" onclick="findNearestSchedules()">找出最近行程</button>
    </div>
  </div>

  <div class="calendar-container">
    <div class="calendar-header">
      <div class="calendar-nav">
        <button class="nav-btn" onclick="previousMonth()">← 上月</button>
        <button class="nav-btn" onclick="nextMonth()">下月 →</button>
      </div>
      <div class="month-year" id="monthYear"></div>
      <button class="nav-btn" onclick="goToToday()">今天</button>
    </div>

    <div class="calendar-grid">
      <div class="calendar-day-header">日</div>
      <div class="calendar-day-header">一</div>
      <div class="calendar-day-header">二</div>
      <div class="calendar-day-header">三</div>
      <div class="calendar-day-header">四</div>
      <div class="calendar-day-header">五</div>
      <div class="calendar-day-header">六</div>
      <!-- 日期格子會由 JS 動態產生 -->
    </div>
  </div>

  <!-- 日程編輯模態框 -->
  <div class="modal" id="scheduleModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="modalTitle">編輯日程</h2>
        <button class="close-btn" onclick="closeModal()">&times;</button>
      </div>

      <form id="scheduleForm">
        <div class="form-group">
          <label>日期</label>
          <input type="date" id="scheduleDate" readonly>
        </div>

        <div class="form-group">
          <label>主要縣市</label>
          <select id="scheduleCity">
            <option value="">請選擇縣市</option>
          </select>
        </div>

        <div class="form-group">
          <label>主要區域</label>
          <select id="scheduleDistrict">
            <option value="">請選擇區域（可選）</option>
          </select>
        </div>

        <div class="form-group">
          <label>日程標題</label>
          <input type="text" id="scheduleTitle" placeholder="例如：台北一日遊">
        </div>

        <div class="form-group">
          <label>備註</label>
          <textarea id="scheduleNotes" placeholder="整體行程備註..."></textarea>
        </div>

        <h3>具體行程項目</h3>
        <button type="button" class="add-item-btn" onclick="addScheduleItem()">+ 新增行程項目</button>
        <div id="scheduleItems"></div>

        <div class="btn-group">
          <button type="submit" class="btn-primary">儲存日程</button>
          <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
          <button type="button" class="btn-danger" onclick="deleteSchedule()" id="deleteBtn" style="display: none;">刪除</button>
        </div>
      </form>
    </div>
  </div>

  <!-- 附近行程查詢結果模態框 -->
  <div class="modal" id="nearbyModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="nearbyModalTitle">附近行程查詢結果</h2>
        <button class="close-btn" onclick="closeNearbyModal()">&times;</button>
      </div>

      <div id="nearbyResults">
        <!-- 動態內容 -->
      </div>

      <div class="btn-group" style="margin-top: 20px;">
        <button type="button" class="btn-primary" onclick="insertSelectedSchedules()">插入</button>
        <button type="button" class="btn-secondary" onclick="closeNearbyModal()">關閉</button>
      </div>
    </div>
  </div>

  <script>
    // 應用常數定義
    const APP_CONSTANTS = {
      CALENDAR: {
        GRID_SIZE: 42,                    // 6週 × 7天的日曆格子數
        DAYS_PER_WEEK: 7                  // 一週天數
      },
      SEARCH: {
        MAX_NEARBY_RESULTS: 8,            // 最多顯示的附近行程數量
        EARTH_RADIUS_KM: 6371,            // 地球半徑（公里）
        DISTANCE_PRECISION: 1             // 距離顯示精度（小數位）
      },
      CACHE: {
        CITIES_TIMEOUT: 10 * 60 * 1000,   // 城市資料快取時間：10分鐘
        TOAST_DURATION: 4000              // Toast提示顯示時間：4秒
      },
      DATE_OFFSETS: {
        MINUS_TWO: -2,
        MINUS_ONE: -1,
        TODAY: 0,
        PLUS_ONE: 1,
        PLUS_TWO: 2
      }
    };

    // HTML 模板常數
    const HTML_TEMPLATES = {
      EMPTY_OPTION: {
        CITY: '<option value="">請選擇縣市</option>',
        DISTRICT_SCHEDULE: '<option value="">請選擇區域（可選）</option>',
        DISTRICT_INPUT: '<option value="">請先選擇縣市</option>',
        DISTRICT: '<option value="">請選擇區域</option>'
      },
      SCHEDULE_ITEM: (item) => \`
        <div class="schedule-item">
          <span class="item-time">\${item.time || ''}</span>
          <span class="item-location">📍 \${item.location}</span>
          <div style="margin-top: 5px; color: #6b7280;">\${item.activity}</div>
          \${item.notes ? \`<div style="margin-top: 3px; font-size: 12px; color: #9ca3af;">\${item.notes}</div>\` : ''}
        </div>
      \`,
      NEARBY_INTRO: '<p style="color: #6b7280; margin-bottom: 20px;">以下是行事曆上距離最近的行程，請勾選想參考的日期：</p>',
      NO_SCHEDULES: '<p>行事曆上目前沒有任何行程</p>',
      MORE_SCHEDULES: (count) => \`<p style="text-align: center; color: #6b7280; margin-top: 10px;">還有 \${count} 個更遠的行程未顯示</p>\`
    };

    // 統一錯誤處理和提示系統
    class ErrorHandler {
      static async handle(operation, fallbackValue = null, userMessage = '操作失敗，請稍後再試') {
        try {
          return await operation();
        } catch (error) {
          console.error('操作失敗:', error);
          this.showToast(userMessage, 'error');
          return fallbackValue;
        }
      }

      static showToast(message, type = 'info') {
        // 如果已存在提示，先移除
        const existingToast = document.querySelector('.toast');
        if (existingToast) existingToast.remove();

        const toast = document.createElement('div');
        toast.className = \`toast toast-\${type}\`;
        toast.innerHTML = \`
          <div class="toast-content">
            <span class="toast-message">\${message}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
          </div>
        \`;
        document.body.appendChild(toast);

        // 自動消失
        setTimeout(() => {
          if (toast.parentElement) toast.remove();
        }, APP_CONSTANTS.CACHE.TOAST_DURATION);
      }

      static showSuccess(message) {
        this.showToast(message, 'success');
      }

      static showError(message) {
        this.showToast(message, 'error');
      }
    }

    // 載入狀態管理類別
    class LoadingManager {
      static show(message = '載入中...') {
        const existing = document.querySelector('.loading-overlay');
        if (existing) return;

        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = \`
          <div class="loading-spinner">
            <div class="spinner"></div>
            <p>\${message}</p>
          </div>
        \`;
        document.body.appendChild(overlay);
      }

      static hide() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
          overlay.remove();
        }
      }
    }

    // API 管理類別
    class ApiManager {
      static async request(url, options = {}) {
        try {
          if (options.showLoading !== false) {
            LoadingManager.show(options.loadingMessage);
          }

          const response = await fetch(url, {
            headers: {
              'Content-Type': 'application/json',
              ...options.headers
            },
            ...options
          });

          if (!response.ok) {
            throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
          }

          const result = await response.json();

          if (options.showLoading !== false) {
            LoadingManager.hide();
          }

          return result;
        } catch (error) {
          LoadingManager.hide();
          console.error(\`API request failed: \${url}\`, error);
          throw error;
        }
      }

      static async get(url, options = {}) {
        return this.request(url, options);
      }

      static async post(url, data, options = {}) {
        return this.request(url, {
          method: 'POST',
          body: JSON.stringify(data),
          loadingMessage: '儲存中...',
          ...options
        });
      }

      static async put(url, data, options = {}) {
        return this.request(url, {
          method: 'PUT',
          body: JSON.stringify(data),
          loadingMessage: '更新中...',
          ...options
        });
      }

      static async delete(url) {
        return this.request(url, {
          method: 'DELETE'
        });
      }
    }

    /**
     * 數據快取管理類別
     * 負責管理城市、區域和座標資料的快取，減少重複的API呼叫
     */
    class DataCache {
      constructor() {
        this.cities = null;
        this.districts = new Map();
        this.coordinates = new Map();
        this.lastCityLoad = 0;
        this.cacheTimeout = APP_CONSTANTS.CACHE.CITIES_TIMEOUT;
      }

      async getCities() {
        const now = Date.now();
        if (!this.cities || (now - this.lastCityLoad > this.cacheTimeout)) {
          this.cities = await ErrorHandler.handle(
            async () => ApiManager.get('/api/cities'),
            [],
            '載入城市清單失敗'
          );
          this.lastCityLoad = now;
        }
        return this.cities;
      }

      async getDistricts(cityId) {
        if (!this.districts.has(cityId)) {
          const districts = await ErrorHandler.handle(
            async () => ApiManager.get(\`/api/cities/\${cityId}/districts\`),
            [],
            '載入區域清單失敗'
          );
          this.districts.set(cityId, districts);
        }
        return this.districts.get(cityId);
      }

      async getCoordinates(districtId) {
        if (!this.coordinates.has(districtId)) {
          const coords = await ErrorHandler.handle(
            async () => {
              const response = await fetch(\`/api/districts/\${districtId}/coordinates\`);
              if (!response.ok) throw new Error('載入座標失敗');
              return response.json();
            },
            null,
            '載入座標失敗'
          );
          if (coords) {
            this.coordinates.set(districtId, coords);
          }
        }
        return this.coordinates.get(districtId);
      }

      clearScheduleCache() {
        // 清除行程相關快取（當有新增/修改/刪除時）
        this.schedules = new Map();
      }
    }

    // 應用狀態管理
    // DOM 元素快取
    const domElements = {
      monthYear: null,
      calendarGrid: null,
      scheduleModal: null,
      nearbyModal: null,
      scheduleForm: null,
      scheduleCity: null,
      scheduleDistrict: null,
      inputCity: null,
      inputDistrict: null,
      nearbyResults: null,
      nearbyModalTitle: null,
      deleteBtn: null,
      scheduleItems: null,
      scheduleDate: null,
      modalTitle: null,
      scheduleTitle: null,
      scheduleNotes: null
    };

    const appState = {
      cache: new DataCache(),
      currentDate: new Date(),
      selectedScheduleId: null,
      allCities: [],
      monthSchedules: {},
      nearbySearchResults: [],
      selectedInputLocation: { cityId: null, districtId: null }
    };

    // 日曆組件類別
    class CalendarComponent {
      constructor(domElements, appState) {
        this.domElements = domElements;
        this.appState = appState;
      }

      /**
       * 渲染日曆視圖
       * 生成包含前後月份日期的完整6週日曆網格
       */
      async render() {
        const year = this.appState.currentDate.getFullYear();
        const month = this.appState.currentDate.getMonth();

        this.domElements.monthYear.textContent = \`\${year}年 \${month + 1}月\`;

        // 計算日曆顯示範圍
        const firstDay = new Date(year, month, 1);
        const startDate = new Date(firstDay);
        // 調整到該週的周日開始顯示（0=周日）
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        const grid = this.domElements.calendarGrid;
        // 清除舊的日期格子（保留星期標題）
        const dayHeaders = grid.querySelectorAll('.calendar-day-header');
        grid.innerHTML = '';
        dayHeaders.forEach(header => grid.appendChild(header));

        // 載入該月份的日程資料
        await this.loadMonthSchedules(year, month);

        // 生成42天的日曆格子 (6週 × 7天)
        for (let i = 0; i < APP_CONSTANTS.CALENDAR.GRID_SIZE; i++) {
          const date = new Date(startDate);
          date.setDate(startDate.getDate() + i);

          const dayElement = this.createDayElement(date, month);
          grid.appendChild(dayElement);
        }
      }

      // 建立日期格子
      createDayElement(date, currentMonth) {
        const day = document.createElement('div');
        day.className = 'calendar-day';

        // 創建新的日期物件避免閉包問題
        const clickDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        day.onclick = () => openScheduleModal(clickDate);

        if (date.getMonth() !== currentMonth) {
          day.classList.add('other-month');
        }

        if (this.isToday(date)) {
          day.classList.add('today');
        }

        const dateStr = formatDate(date);
        const schedule = this.appState.monthSchedules[dateStr];

        if (schedule) {
          day.classList.add('has-schedule');
        }

        day.innerHTML = \`
          <div class="day-number">\${date.getDate()}</div>
          \${schedule ? \`
            <div class="day-region">\${schedule.city_name || ''}\${schedule.district_name ? ' - ' + schedule.district_name : ''}</div>
            <div class="day-title">\${schedule.title || ''}</div>
          \` : ''}
        \`;

        return day;
      }

      // 載入月份日程
      async loadMonthSchedules(year, month) {
        try {
          const startDate = \`\${year}-\${String(month + 1).padStart(2, '0')}-01\`;
          const endDate = \`\${year}-\${String(month + 2).padStart(2, '0')}-01\`;

          const url = \`/api/schedules?start=\${startDate}&end=\${endDate}\`;
          const response = await fetch(url);
          const schedules = await response.json();

          // 清空舊資料
          this.appState.monthSchedules = {};

          // 將日程按日期分組
          schedules.forEach(schedule => {
            this.appState.monthSchedules[schedule.date] = schedule;
          });
        } catch (error) {
          console.error('Failed to load month schedules:', error);
          ErrorHandler.showError('載入月份日程失敗');
        }
      }

      isToday(date) {
        const today = new Date();
        return date.toDateString() === today.toDateString();
      }

      async previousMonth() {
        this.appState.currentDate.setMonth(this.appState.currentDate.getMonth() - 1);
        await this.render();
      }

      async nextMonth() {
        this.appState.currentDate.setMonth(this.appState.currentDate.getMonth() + 1);
        await this.render();
      }

      async goToToday() {
        this.appState.currentDate = new Date();
        await this.render();
      }
    }

    // 初始化DOM元素快取
    function initializeDOMElements() {
      domElements.monthYear = document.getElementById('monthYear');
      domElements.calendarGrid = document.querySelector('.calendar-grid');
      domElements.scheduleModal = document.getElementById('scheduleModal');
      domElements.nearbyModal = document.getElementById('nearbyModal');
      domElements.scheduleForm = document.getElementById('scheduleForm');
      domElements.scheduleCity = document.getElementById('scheduleCity');
      domElements.scheduleDistrict = document.getElementById('scheduleDistrict');
      domElements.inputCity = document.getElementById('inputCity');
      domElements.inputDistrict = document.getElementById('inputDistrict');
      domElements.nearbyResults = document.getElementById('nearbyResults');
      domElements.nearbyModalTitle = document.getElementById('nearbyModalTitle');
      domElements.deleteBtn = document.getElementById('deleteBtn');
      domElements.scheduleItems = document.getElementById('scheduleItems');
      domElements.scheduleDate = document.getElementById('scheduleDate');
      domElements.modalTitle = document.getElementById('modalTitle');
      domElements.scheduleTitle = document.getElementById('scheduleTitle');
      domElements.scheduleNotes = document.getElementById('scheduleNotes');
    }

    // 主應用程式類別
    class BookingApp {
      constructor() {
        this.calendar = null;
      }

      async initialize() {
        initializeDOMElements();
        this.calendar = new CalendarComponent(domElements, appState);
        this.initializeEventListeners();
        await this.loadCities();
        await this.calendar.render();
      }

      initializeEventListeners() {
        // 日曆導航事件
        const previousButton = document.querySelector('.nav-btn:nth-child(1)');
        const nextButton = document.querySelector('.nav-btn:nth-child(2)');
        const todayButton = document.querySelector('.nav-btn:last-child');

        if (previousButton) previousButton.onclick = () => this.calendar.previousMonth();
        if (nextButton) nextButton.onclick = () => this.calendar.nextMonth();
        if (todayButton) todayButton.onclick = () => this.calendar.goToToday();

        // 表單事件
        domElements.scheduleCity.addEventListener('change', (e) => {
          const cityId = e.target.value;
          if (cityId) {
            loadDistrictsForSchedule(cityId);
          } else {
            domElements.scheduleDistrict.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.DISTRICT_SCHEDULE;
          }
        });

        domElements.scheduleForm.addEventListener('submit', async (e) => {
          e.preventDefault();
          await this.handleScheduleSubmit();
        });

        domElements.inputCity.addEventListener('change', (e) => {
          const cityId = e.target.value;
          if (cityId) {
            loadDistrictsForInput(cityId);
          } else {
            domElements.inputDistrict.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.DISTRICT_INPUT;
          }
        });
      }

      async handleScheduleSubmit() {
        const dateValue = domElements.scheduleDate.value;
        const title = domElements.scheduleTitle.value.trim();

        // 表單驗證
        if (!dateValue) {
          ErrorHandler.showError('請選擇日期');
          return;
        }

        if (!title) {
          ErrorHandler.showError('請輸入行程標題');
          return;
        }

        if (title.length > 100) {
          ErrorHandler.showError('行程標題不能超過100個字元');
          return;
        }

        try {
          const method = appState.selectedScheduleId ? 'PUT' : 'POST';
          const url = appState.selectedScheduleId ? \`/api/schedules/\${appState.selectedScheduleId}\` : '/api/schedules';

          const scheduleData = {
            date: dateValue,
            city_id: domElements.scheduleCity.value || null,
            district_id: domElements.scheduleDistrict.value || null,
            title: title,
            notes: domElements.scheduleNotes.value.trim()
          };

          if (method === 'POST') {
            await ApiManager.post('/api/schedules', scheduleData);
          } else {
            await ApiManager.put(\`/api/schedules/\${appState.selectedScheduleId}\`, scheduleData);
          }

          ErrorHandler.showSuccess(appState.selectedScheduleId ? '日程已更新' : '日程已創建');
          closeModal();
          await this.calendar.render();
        } catch (error) {
          console.error('Failed to save schedule:', error);
          ErrorHandler.showError('儲存失敗，請稍後再試');
        }
      }

      async loadCities() {
        appState.allCities = await appState.cache.getCities();

        // 填充選單
        const cityOptions = appState.allCities.map(city => \`<option value="\${city.id}">\${city.name}</option>\`).join('');

        domElements.scheduleCity.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.CITY + cityOptions;
        domElements.inputCity.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.CITY + cityOptions;
      }
    }

    // 全域應用程式實例
    let app;

    // 初始化
    document.addEventListener('DOMContentLoaded', async function() {
      app = new BookingApp();
      await app.initialize();
    });

    // 載入城市資料（使用快取）
    async function loadCities() {
      appState.allCities = await appState.cache.getCities();

      // 填充選單
      const cityOptions = appState.allCities.map(city => \`<option value="\${city.id}">\${city.name}</option>\`).join('');

      domElements.scheduleCity.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.CITY + cityOptions;
      domElements.inputCity.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.CITY + cityOptions;
    }

    /**
     * 渲染日曆視圖
     * 生成包含前後月份日期的完整6週日曆網格
     */
    async function renderCalendar() {
      const year = appState.currentDate.getFullYear();
      const month = appState.currentDate.getMonth();

      domElements.monthYear.textContent = \`\${year}年 \${month + 1}月\`;

      // 計算日曆顯示範圍
      const firstDay = new Date(year, month, 1);
      const startDate = new Date(firstDay);
      // 調整到該週的周日開始顯示（0=周日）
      startDate.setDate(startDate.getDate() - firstDay.getDay());

      const grid = domElements.calendarGrid;
      // 清除舊的日期格子（保留星期標題）
      const dayHeaders = grid.querySelectorAll('.calendar-day-header');
      grid.innerHTML = '';
      dayHeaders.forEach(header => grid.appendChild(header));

      // 載入該月份的日程資料
      await loadMonthSchedules(year, month);

      // 生成42天的日曆格子 (6週 × 7天)
      for (let i = 0; i < APP_CONSTANTS.CALENDAR.GRID_SIZE; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);

        const dayElement = createDayElement(date, month);
        grid.appendChild(dayElement);
      }
    }

    // 建立日期格子
    function createDayElement(date, currentMonth) {
      const day = document.createElement('div');
      day.className = 'calendar-day';

      // 創建新的日期物件避免閉包問題
      const clickDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      day.onclick = () => openScheduleModal(clickDate);

      if (date.getMonth() !== currentMonth) {
        day.classList.add('other-month');
      }

      if (isToday(date)) {
        day.classList.add('today');
      }

      const dateStr = formatDate(date);
      const schedule = appState.monthSchedules[dateStr];

      if (schedule) {
        day.classList.add('has-schedule');
      }

      day.innerHTML = \`
        <div class="day-number">\${date.getDate()}</div>
        \${schedule ? \`
          <div class="day-region">\${schedule.city_name || ''}\${schedule.district_name ? ' - ' + schedule.district_name : ''}</div>
          <div class="day-title">\${schedule.title || ''}</div>
        \` : ''}
      \`;

      return day;
    }

    // 載入月份日程
    async function loadMonthSchedules(year, month) {
      try {
        const startDate = \`\${year}-\${String(month + 1).padStart(2, '0')}-01\`;
        const endDate = \`\${year}-\${String(month + 2).padStart(2, '0')}-01\`;

        const url = \`/api/schedules?start=\${startDate}&end=\${endDate}\`;
        const response = await fetch(url);
        const schedules = await response.json();

        appState.monthSchedules = {};
        schedules.forEach(schedule => {
          appState.monthSchedules[schedule.date] = schedule;
        });
      } catch (error) {
        console.error('Failed to load schedules:', error);
        appState.monthSchedules = {};
      }
    }


    // 工具函數
    /**
     * 時區安全的日期格式化函數
     * 避免使用 toISOString() 造成的時區轉換問題
     * 將時間設定為中午12:00以避免跨日邊界問題
     * @param {Date} date - 要格式化的日期物件
     * @returns {string} YYYY-MM-DD 格式的日期字串
     */
    function formatDate(date) {
      // 創建一個新的日期物件設定為中午，避免時區轉換問題
      const safeDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 12, 0, 0);
      const year = safeDate.getFullYear();
      const month = String(safeDate.getMonth() + 1).padStart(2, '0');
      const day = String(safeDate.getDate()).padStart(2, '0');
      return \`\${year}-\${month}-\${day}\`;
    }

    function isToday(date) {
      const today = new Date();
      return date.toDateString() === today.toDateString();
    }

    // 日曆導航
    async function previousMonth() {
      currentDate.setMonth(currentDate.getMonth() - 1);
      await renderCalendar();
    }

    async function nextMonth() {
      currentDate.setMonth(currentDate.getMonth() + 1);
      await renderCalendar();
    }

    async function goToToday() {
      currentDate = new Date();
      await renderCalendar();
    }





    // 開啟日程編輯模態框
    function openScheduleModal(date) {
      const dateStr = formatDate(date);
      const schedule = appState.monthSchedules[dateStr];

      domElements.scheduleDate.value = dateStr;
      domElements.modalTitle.textContent = \`\${date.getFullYear()}年\${date.getMonth() + 1}月\${date.getDate()}日 行程\`;

      if (schedule) {
        // 編輯現有日程
        appState.selectedScheduleId = schedule.id;
        domElements.scheduleCity.value = schedule.city_id || '';
        domElements.scheduleTitle.value = schedule.title || '';
        domElements.scheduleNotes.value = schedule.notes || '';
        domElements.deleteBtn.style.display = 'block';

        if (schedule.city_id) {
          loadDistrictsForSchedule(schedule.city_id).then(() => {
            domElements.scheduleDistrict.value = schedule.district_id || '';
          });
        }

        loadScheduleItems(schedule.id);
      } else {
        // 新增日程
        appState.selectedScheduleId = null;
        domElements.scheduleForm.reset();
        domElements.scheduleDate.value = dateStr;
        domElements.scheduleItems.innerHTML = '';
        domElements.deleteBtn.style.display = 'none';
      }

      domElements.scheduleModal.classList.add('active');
    }

    function closeModal() {
      domElements.scheduleModal.classList.remove('active');
    }

    // 載入日程項目
    async function loadScheduleItems(scheduleId) {
      try {
        const response = await fetch(\`/api/schedules/\${scheduleId}/items\`);
        const items = await response.json();

        domElements.scheduleItems.innerHTML = items.map(item => HTML_TEMPLATES.SCHEDULE_ITEM(item)).join('');
      } catch (error) {
        console.error('Failed to load schedule items:', error);
      }
    }

    // 縣市變更時載入區域
    function initializeEventListeners() {
      domElements.scheduleCity.addEventListener('change', function(e) {
        const cityId = e.target.value;
        if (cityId) {
          loadDistrictsForSchedule(cityId);
        } else {
          domElements.scheduleDistrict.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.DISTRICT_SCHEDULE;
        }
      });

      domElements.scheduleForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const dateValue = domElements.scheduleDate.value;
        const title = domElements.scheduleTitle.value.trim();

        // 表單驗證
        if (!dateValue) {
          ErrorHandler.showError('請選擇日期');
          return;
        }

        if (!title) {
          ErrorHandler.showError('請輸入行程標題');
          return;
        }

        if (title.length > 100) {
          ErrorHandler.showError('行程標題不能超過100個字元');
          return;
        }

        try {
          const method = appState.selectedScheduleId ? 'PUT' : 'POST';
          const url = appState.selectedScheduleId ? \`/api/schedules/\${appState.selectedScheduleId}\` : '/api/schedules';

          const scheduleData = {
            date: dateValue,
            city_id: domElements.scheduleCity.value || null,
            district_id: domElements.scheduleDistrict.value || null,
            title: title,
            notes: domElements.scheduleNotes.value.trim()
          };

          const response = await fetch(url, {
            method: method,
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(scheduleData)
          });

          if (response.ok) {
            ErrorHandler.showSuccess(appState.selectedScheduleId ? '日程已更新' : '日程已創建');
            closeModal();
            await renderCalendar();
          } else {
            throw new Error('儲存失敗');
          }
        } catch (error) {
          console.error('Failed to save schedule:', error);
          ErrorHandler.showError('儲存失敗，請稍後再試');
        }
      });

      domElements.inputCity.addEventListener('change', function(e) {
        const cityId = e.target.value;
        if (cityId) {
          loadDistrictsForInput(cityId);
        } else {
          domElements.inputDistrict.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.DISTRICT_INPUT;
        }
      });
    }

    async function loadDistrictsForSchedule(cityId) {
      try {
        const districts = await appState.cache.getDistricts(cityId);

        if (!domElements.scheduleDistrict) {
          console.error('Schedule district element not found');
          return;
        }

        domElements.scheduleDistrict.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.DISTRICT_SCHEDULE +
          districts.map(district => \`<option value="\${district.id}">\${district.name}</option>\`).join('');
      } catch (error) {
        console.error('Failed to load districts for schedule:', error);
        ErrorHandler.showError('載入區域清單失敗');
      }
    }


    // 刪除日程
    async function deleteSchedule() {
      if (!appState.selectedScheduleId) return;

      if (confirm('確定要刪除這個日程嗎？')) {
        try {
          const response = await fetch(\`/api/schedules/\${appState.selectedScheduleId}\`, {
            method: 'DELETE'
          });

          if (response.ok) {
            closeModal();
            await renderCalendar();
          }
        } catch (error) {
          console.error('Failed to delete schedule:', error);
          ErrorHandler.showError('刪除失敗，請稍後再試');
        }
      }
    }

    /**
     * 使用 Haversine 公式計算兩個地理座標之間的距離
     * @param {number} lat1 - 第一個點的緯度
     * @param {number} lon1 - 第一個點的經度
     * @param {number} lat2 - 第二個點的緯度
     * @param {number} lon2 - 第二個點的經度
     * @returns {number} 兩點間的距離（公里）
     */
    function calculateDistance(lat1, lon1, lat2, lon2) {
      const R = APP_CONSTANTS.SEARCH.EARTH_RADIUS_KM; // 地球半徑 (公里)
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLon = (lon2 - lon1) * Math.PI / 180;
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    }

    // 輸入縣市變更時載入區域
    document.getElementById('inputCity').addEventListener('change', function(e) {
      const cityId = e.target.value;
      if (cityId) {
        loadDistrictsForInput(cityId);
      } else {
        domElements.inputDistrict.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.DISTRICT_INPUT;
      }
    });

    async function loadDistrictsForInput(cityId) {
      try {
        if (!cityId) {
          throw new Error('City ID is required');
        }

        const districts = await appState.cache.getDistricts(cityId);

        if (!domElements.inputDistrict) {
          console.error('Input district element not found');
          return;
        }

        domElements.inputDistrict.innerHTML = HTML_TEMPLATES.EMPTY_OPTION.DISTRICT +
          districts.map(district => \`<option value="\${district.id}">\${district.name}</option>\`).join('');
      } catch (error) {
        console.error('Failed to load districts for input:', error);
        ErrorHandler.showError('載入區域清單失敗');
      }
    }

    // 座標快取
    let coordinatesCache = {};

    // 找出最近的行程
    // 預載所有行程地點的座標
    async function preloadScheduleCoordinates(schedules) {
      const uniqueDistrictIds = [...new Set(schedules.map(s => s.district_id))];
      const coordsPromises = uniqueDistrictIds.map(id => getDistrictCoordinatesWithCache(id));
      await Promise.all(coordsPromises);
    }

    // 計算行程與指定地點的距離
    function calculateScheduleDistances(schedules, inputCoords) {
      const schedulesWithDistance = [];

      for (const schedule of schedules) {
        const scheduleCoords = coordinatesCache[schedule.district_id];
        if (scheduleCoords) {
          const distance = calculateDistance(
            inputCoords.lat, inputCoords.lng,
            scheduleCoords.lat, scheduleCoords.lng
          );
          schedulesWithDistance.push({
            ...schedule,
            distance: distance
          });
        }
      }

      return schedulesWithDistance.sort((a, b) => a.distance - b.distance);
    }

    /**
     * 尋找距離指定地點最近的行程
     * 使用地理座標計算距離，並按距離排序顯示結果
     */
    async function findNearestSchedules() {
      const inputCityId = domElements.inputCity.value;
      const inputDistrictId = domElements.inputDistrict.value;

      if (!inputCityId || !inputDistrictId) {
        ErrorHandler.showError('請先選擇完整的縣市和區域');
        return;
      }

      try {
        // 取得輸入地區的經緯度
        const inputCoords = await getDistrictCoordinatesWithCache(inputDistrictId);
        if (!inputCoords) {
          ErrorHandler.showError('無法取得該地區的座標資訊');
          return;
        }

        // 取得所有行程
        const response = await fetch('/api/schedules');
        const allSchedules = await response.json();

        if (allSchedules.length === 0) {
          ErrorHandler.showError('行事曆上目前沒有任何行程');
          return;
        }

        // 預載所有座標並計算距離
        await preloadScheduleCoordinates(allSchedules);
        const schedulesWithDistance = calculateScheduleDistances(allSchedules, inputCoords);

        // 存儲搜索結果和輸入位置
        appState.nearbySearchResults = schedulesWithDistance;
        appState.selectedInputLocation = { cityId: inputCityId, districtId: inputDistrictId };

        // 顯示結果
        showNearestResults(schedulesWithDistance, inputCityId, inputDistrictId);

      } catch (error) {
        console.error('Failed to find nearest schedules:', error);
        ErrorHandler.showError('查詢失敗，請稍後再試');
      }
    }

    // 帶快取的座標取得
    async function getDistrictCoordinatesWithCache(districtId) {
      if (coordinatesCache[districtId]) {
        return coordinatesCache[districtId];
      }

      try {
        const response = await fetch(\`/api/districts/\${districtId}/coordinates\`);
        if (response.ok) {
          const coords = await response.json();
          coordinatesCache[districtId] = coords;
          return coords;
        }
        return null;
      } catch (error) {
        console.error('Failed to get coordinates:', error);
        return null;
      }
    }

    // 取得區域座標
    async function getDistrictCoordinates(districtId) {
      try {
        const response = await fetch(\`/api/districts/\${districtId}/coordinates\`);
        if (response.ok) {
          return await response.json();
        }
        return null;
      } catch (error) {
        console.error('Failed to get coordinates:', error);
        return null;
      }
    }

    // 顯示最近行程結果
    // 產生日期選擇選項的HTML
    function generateDateOptions(schedule) {
      return \`
        <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center;">
          <input type="checkbox" id="schedule_\${schedule.id}_minus2" style="transform: scale(1.1);">
          <label for="schedule_\${schedule.id}_minus2" style="font-size: 12px; color: #6b7280;">前2天</label>

          <input type="checkbox" id="schedule_\${schedule.id}_minus1" style="transform: scale(1.1);">
          <label for="schedule_\${schedule.id}_minus1" style="font-size: 12px; color: #6b7280;">前1天</label>

          <input type="checkbox" id="schedule_\${schedule.id}" style="transform: scale(1.2);">
          <label for="schedule_\${schedule.id}" style="color: #059669; font-weight: bold;">當天</label>

          <input type="checkbox" id="schedule_\${schedule.id}_plus1" style="transform: scale(1.1);">
          <label for="schedule_\${schedule.id}_plus1" style="font-size: 12px; color: #6b7280;">後1天</label>

          <input type="checkbox" id="schedule_\${schedule.id}_plus2" style="transform: scale(1.1);">
          <label for="schedule_\${schedule.id}_plus2" style="font-size: 12px; color: #6b7280;">後2天</label>
        </div>
      \`;
    }

    // 產生單個行程卡片的HTML
    function generateScheduleCard(schedule, index) {
      const isClosest = index === 0;
      const borderColor = isClosest ? '#10b981' : '#e5e7eb';
      const bgColor = isClosest ? '#f0fdf4' : '#ffffff';

      return \`
        <div style="border: 2px solid \${borderColor}; background: \${bgColor}; padding: 15px; margin: 10px 0; border-radius: 8px; \${isClosest ? 'box-shadow: 0 4px 6px rgba(16, 185, 129, 0.1);' : ''}">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="flex: 1;">
              <div style="display: flex; align-items: center; gap: 10px;">
                <strong style="color: #1f2937; font-size: 16px;">\${schedule.date}</strong>
                \${isClosest ? '<span style="background: #10b981; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">最近</span>' : ''}
              </div>
              <div style="color: #059669; font-weight: 500; margin: 5px 0;">
                📍 \${schedule.city_name}\${schedule.district_name}
              </div>
              <div style="color: #6b7280; font-size: 14px;">
                \${schedule.title || '無標題'}
              </div>
            </div>
            <div style="text-align: right; margin-left: 20px;">
              <div style="color: \${isClosest ? '#059669' : '#f59e0b'}; font-weight: bold; font-size: 16px;">
                距離 \${schedule.distance.toFixed(1)} 公里
              </div>
              <div style="margin-top: 8px;">
                \${generateDateOptions(schedule)}
              </div>
            </div>
          </div>
        </div>
      \`;
    }

    // 產生行程列表的HTML
    function generateScheduleListHtml(schedules) {
      if (schedules.length === 0) {
        return HTML_TEMPLATES.NO_SCHEDULES;
      }

      let html = '<div style="max-height: 400px; overflow-y: auto;">';

      // 只顯示最近的 8 個行程
      schedules.slice(0, APP_CONSTANTS.SEARCH.MAX_NEARBY_RESULTS).forEach((schedule, index) => {
        html += generateScheduleCard(schedule, index);
      });

      if (schedules.length > APP_CONSTANTS.SEARCH.MAX_NEARBY_RESULTS) {
        const remainingCount = schedules.length - APP_CONSTANTS.SEARCH.MAX_NEARBY_RESULTS;
        html += HTML_TEMPLATES.MORE_SCHEDULES(remainingCount);
      }

      html += '</div>';
      return html;
    }

    function showNearestResults(schedules, inputCityId, inputDistrictId) {
      const inputCityName = appState.allCities.find(c => c.id == inputCityId)?.name || '';
      domElements.nearbyModalTitle.textContent = \`從「\${inputCityName}」出發的最近行程\`;

      const introText = HTML_TEMPLATES.NEARBY_INTRO;
      const scheduleListHtml = generateScheduleListHtml(schedules);

      domElements.nearbyResults.innerHTML = introText + scheduleListHtml;
      domElements.nearbyModal.classList.add('active');
    }

    // 解析checkbox ID來獲取日期偏移
    function parseDateOffset(checkboxId) {
      const parts = checkboxId.split('_');
      const scheduleId = parseInt(parts[1]);
      let dayOffset = 0;

      if (parts.length > 2) {
        const offsetMap = {
          'minus1': APP_CONSTANTS.DATE_OFFSETS.MINUS_ONE,
          'minus2': APP_CONSTANTS.DATE_OFFSETS.MINUS_TWO,
          'plus1': APP_CONSTANTS.DATE_OFFSETS.PLUS_ONE,
          'plus2': APP_CONSTANTS.DATE_OFFSETS.PLUS_TWO
        };
        dayOffset = offsetMap[parts[2]] || 0;
      }

      return { scheduleId, dayOffset };
    }

    // 創建新行程數據
    function createScheduleData(originalSchedule, dayOffset) {
      const originalDate = new Date(originalSchedule.date);
      const newDate = new Date(originalDate);
      newDate.setDate(originalDate.getDate() + dayOffset);
      const newDateStr = formatDate(newDate);

      const noteText = dayOffset === 0 ? '參考行程' :
        \`參考行程（\${dayOffset > 0 ? '延後' : '提前'}\${Math.abs(dayOffset)}天）\`;

      return {
        date: newDateStr,
        city_id: appState.selectedInputLocation.cityId,
        district_id: appState.selectedInputLocation.districtId,
        title: originalSchedule.title || '新行程',
        notes: noteText
      };
    }

    // 創建單個行程
    async function createSingleSchedule(scheduleData) {
      const response = await fetch('/api/schedules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(scheduleData)
      });
      return response.ok;
    }

    // 插入選中的日程
    async function insertSelectedSchedules() {
      try {
        const checkboxes = document.querySelectorAll('#nearbyResults input[type="checkbox"]:checked');
        if (checkboxes.length === 0) {
          ErrorHandler.showError('請至少選擇一個日期');
          return;
        }

        let insertedCount = 0;

        for (const checkbox of checkboxes) {
          const { scheduleId, dayOffset } = parseDateOffset(checkbox.id);

          const originalSchedule = appState.nearbySearchResults.find(s => s.id === scheduleId);
          if (!originalSchedule) continue;

          const scheduleData = createScheduleData(originalSchedule, dayOffset);
          const success = await createSingleSchedule(scheduleData);

          if (success) {
            insertedCount++;
          }
        }

        if (insertedCount > 0) {
          ErrorHandler.showSuccess(\`已成功插入 \${insertedCount} 個行程\`);
          await renderCalendar();
          closeNearbyModal();
        } else {
          ErrorHandler.showError('沒有插入任何行程');
        }

      } catch (error) {
        console.error('Failed to insert schedules:', error);
        ErrorHandler.showError('插入行程失敗，請稍後再試');
      }
    }

    // 關閉附近行程模態框
    function closeNearbyModal() {
      domElements.nearbyModal.classList.remove('active');
    }
  </script>
</body>
</html>`);
});

// API: 取得所有城市
app.get('/api/cities', async (c) => {
  try {
    const results = await c.env.DB.prepare('SELECT * FROM cities ORDER BY sort_order, name').all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch cities' }, 500);
  }
});

// API: 取得特定城市的所有區域
app.get('/api/cities/:id/districts', async (c) => {
  const cityId = c.req.param('id');
  try {
    const results = await c.env.DB.prepare(
      'SELECT * FROM districts WHERE city_id = ? ORDER BY sort_order, name'
    ).bind(cityId).all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch districts' }, 500);
  }
});

// API: 取得區域座標
app.get('/api/districts/:id/coordinates', async (c) => {
  const districtId = c.req.param('id');
  try {
    const results = await c.env.DB.prepare(
      'SELECT latitude, longitude FROM districts WHERE id = ?'
    ).bind(districtId).all();

    if (results.results.length === 0) {
      return c.json({ error: 'District not found' }, 404);
    }

    const district = results.results[0];
    return c.json({
      lat: parseFloat(district.latitude),
      lng: parseFloat(district.longitude)
    });
  } catch (error) {
    return c.json({ error: 'Failed to fetch coordinates' }, 500);
  }
});

// API: 取得日程（支援日期範圍和地區篩選）
app.get('/api/schedules', async (c) => {
  const { start, end, city_id, district_id } = c.req.query();

  let query = `
    SELECT ds.*, c.name as city_name, d.name as district_name
    FROM daily_schedules ds
    LEFT JOIN cities c ON ds.city_id = c.id
    LEFT JOIN districts d ON ds.district_id = d.id
    WHERE 1=1
  `;

  const params = [];

  if (start) {
    query += ' AND ds.date >= ?';
    params.push(start);
  }

  if (end) {
    query += ' AND ds.date < ?';
    params.push(end);
  }

  if (city_id) {
    query += ' AND ds.city_id = ?';
    params.push(city_id);
  }

  if (district_id) {
    query += ' AND ds.district_id = ?';
    params.push(district_id);
  }

  query += ' ORDER BY ds.date';

  try {
    const results = await c.env.DB.prepare(query).bind(...params).all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch schedules' }, 500);
  }
});

// API: 取得特定日程的項目
app.get('/api/schedules/:id/items', async (c) => {
  const scheduleId = c.req.param('id');
  try {
    const results = await c.env.DB.prepare(
      'SELECT * FROM schedule_items WHERE daily_schedule_id = ? ORDER BY order_index, time'
    ).bind(scheduleId).all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch schedule items' }, 500);
  }
});

// API: 新增日程
app.post('/api/schedules', async (c) => {
  const body = await c.req.json();
  const { date, city_id, district_id, title, notes } = body;

  try {
    const result = await c.env.DB.prepare(
      `INSERT INTO daily_schedules (date, city_id, district_id, title, notes)
       VALUES (?, ?, ?, ?, ?)`
    ).bind(date, city_id || null, district_id || null, title, notes).run();

    return c.json({ id: result.meta.last_row_id, ...body }, 201);
  } catch (error) {
    return c.json({ error: 'Failed to create schedule' }, 500);
  }
});

// API: 更新日程
app.put('/api/schedules/:id', async (c) => {
  const id = c.req.param('id');
  const body = await c.req.json();
  const { date, city_id, district_id, title, notes } = body;

  try {
    await c.env.DB.prepare(
      `UPDATE daily_schedules
       SET date = ?, city_id = ?, district_id = ?, title = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`
    ).bind(date, city_id || null, district_id || null, title, notes, id).run();

    return c.json({ id, ...body });
  } catch (error) {
    return c.json({ error: 'Failed to update schedule' }, 500);
  }
});

// API: 刪除日程
app.delete('/api/schedules/:id', async (c) => {
  const id = c.req.param('id');

  try {
    await c.env.DB.prepare('DELETE FROM daily_schedules WHERE id = ?').bind(id).run();
    return c.json({ message: 'Schedule deleted' });
  } catch (error) {
    return c.json({ error: 'Failed to delete schedule' }, 500);
  }
});

// API: 新增日程項目
app.post('/api/schedules/:id/items', async (c) => {
  const scheduleId = c.req.param('id');
  const body = await c.req.json();
  const { time, location, activity, notes, latitude, longitude, order_index } = body;

  try {
    const result = await c.env.DB.prepare(
      `INSERT INTO schedule_items (daily_schedule_id, time, location, activity, notes, latitude, longitude, order_index)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
    ).bind(scheduleId, time, location, activity, notes, latitude || null, longitude || null, order_index || 0).run();

    return c.json({ id: result.meta.last_row_id, daily_schedule_id: scheduleId, ...body }, 201);
  } catch (error) {
    return c.json({ error: 'Failed to add schedule item' }, 500);
  }
});

export default app;