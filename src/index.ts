import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { citiesRouter } from './routes/cities';
import { districtsRouter } from './routes/districts';
import { schedulesRouter } from './routes/schedules';
import { errorHandler } from './utils/errors';

type Env = {
  DB: D1Database;
};

const app = new Hono<{ Bindings: Env }>();

// 中間件
app.use('/*', cors());

// 錯誤處理中間件
app.onError(errorHandler);

// API 路由
app.route('/api/cities', citiesRouter);
app.route('/api/districts', districtsRouter);
app.route('/api/schedules', schedulesRouter);

// 主頁 - 行事曆介面
app.get('/', async (c) => {
  try {
    const htmlContent = `<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SGS排行程</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; background: #f5f5f5; }
    .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
    .header h1 { margin-bottom: 20px; }
    .distance-section { display: flex; gap: 15px; align-items: center; justify-content: center; flex-wrap: wrap; }
    .filter-group { display: flex; align-items: center; gap: 8px; }
    .filter-group select { padding: 8px 12px; border: none; border-radius: 6px; min-width: 120px; }
    .nav-btn { background: #3b82f6; color: white; border: none; padding: 10px 15px; border-radius: 6px; cursor: pointer; }
    .nav-btn:hover { background: #2563eb; }
    .calendar-container { max-width: 1200px; margin: 30px auto; padding: 0 20px; }
    .calendar-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
    .calendar-nav { display: flex; gap: 10px; }
    .month-year { font-size: 24px; font-weight: bold; color: #1f2937; }
    .calendar-grid { display: grid; grid-template-columns: repeat(7, 1fr); gap: 1px; background: #e5e7eb; border-radius: 8px; overflow: hidden; }
    .calendar-day-header { background: #f3f4f6; padding: 12px; text-align: center; font-weight: 600; color: #374151; }
    .calendar-day { background: white; min-height: 120px; padding: 8px; cursor: pointer; transition: all 0.2s; }
    .calendar-day:hover { background: #f9fafb; }
    .calendar-day.other-month { background: #f9fafb; color: #9ca3af; }
    .calendar-day.today { background: #dbeafe; border: 2px solid #3b82f6; }
    .calendar-day.has-schedule { background: #fef3c7; border-left: 4px solid #f59e0b; }
    .day-number { font-weight: 600; margin-bottom: 4px; }
    .day-region { font-size: 12px; color: #059669; font-weight: 500; margin-bottom: 2px; }
    .day-title { font-size: 11px; color: #374151; line-height: 1.2; overflow: hidden; }
    .modal { display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 1000; }
    .modal.active { display: flex; }
    .modal-content { background: white; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; max-height: 80vh; overflow-y: auto; }
    .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
    .close-btn { background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; }
    .form-group { margin-bottom: 15px; }
    .form-group label { display: block; margin-bottom: 5px; color: #374151; font-weight: 500; }
    .form-group input, .form-group textarea, .form-group select { width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
    .form-group textarea { resize: vertical; min-height: 80px; }
    .btn-group { display: flex; gap: 10px; margin-top: 20px; }
    .btn-primary { background: #3b82f6; color: white; padding: 12px 20px; border: none; border-radius: 6px; cursor: pointer; flex: 1; }
    .btn-secondary { background: #6b7280; color: white; padding: 12px 20px; border: none; border-radius: 6px; cursor: pointer; flex: 1; }
    .btn-danger { background: #dc2626; color: white; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; }
    .add-item-btn { background: #10b981; color: white; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; margin-bottom: 15px; }
    .schedule-items { margin-top: 20px; }
    .schedule-item { background: #f9fafb; padding: 15px; margin-bottom: 10px; border-radius: 6px; border-left: 4px solid #3b82f6; }
    .item-time { color: #3b82f6; font-weight: 600; margin-right: 10px; }
    .item-location { color: #059669; margin-right: 10px; }
  </style>
</head>
<body>
  <div class="header">
    <h1>🗓️ SGS排行程</h1>
    <div class="distance-section">
      <div class="filter-group">
        <label>輸入地區查詢附近行程：</label>
        <select id="inputCity">
          <option value="">請選擇縣市</option>
        </select>
      </div>
      <div class="filter-group">
        <select id="inputDistrict">
          <option value="">請先選擇縣市</option>
        </select>
      </div>
      <button class="nav-btn" onclick="findNearestSchedules()">找出最近行程</button>
    </div>
  </div>

  <div class="calendar-container">
    <div class="calendar-header">
      <div class="calendar-nav">
        <button class="nav-btn" onclick="previousMonth()">← 上月</button>
        <button class="nav-btn" onclick="nextMonth()">下月 →</button>
      </div>
      <div class="month-year" id="monthYear"></div>
      <button class="nav-btn" onclick="goToToday()">今天</button>
    </div>

    <div class="calendar-grid">
      <div class="calendar-day-header">日</div>
      <div class="calendar-day-header">一</div>
      <div class="calendar-day-header">二</div>
      <div class="calendar-day-header">三</div>
      <div class="calendar-day-header">四</div>
      <div class="calendar-day-header">五</div>
      <div class="calendar-day-header">六</div>
    </div>
  </div>

  <!-- 日程編輯模態框 -->
  <div class="modal" id="scheduleModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="modalTitle">編輯日程</h2>
        <button class="close-btn" onclick="closeModal()">&times;</button>
      </div>
      <form id="scheduleForm">
        <div class="form-group">
          <label>日期</label>
          <input type="date" id="scheduleDate" readonly>
        </div>
        <div class="form-group">
          <label>主要縣市</label>
          <select id="scheduleCity">
            <option value="">請選擇縣市</option>
          </select>
        </div>
        <div class="form-group">
          <label>主要區域</label>
          <select id="scheduleDistrict">
            <option value="">請選擇區域（可選）</option>
          </select>
        </div>
        <div class="form-group">
          <label>日程標題</label>
          <input type="text" id="scheduleTitle" placeholder="例如：台北一日遊">
        </div>
        <div class="form-group">
          <label>備註</label>
          <textarea id="scheduleNotes" placeholder="整體行程備註..."></textarea>
        </div>
        <h3>具體行程項目</h3>
        <button type="button" class="add-item-btn" onclick="addScheduleItem()">+ 新增行程項目</button>
        <div id="scheduleItems"></div>
        <div class="btn-group">
          <button type="submit" class="btn-primary">儲存日程</button>
          <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
          <button type="button" class="btn-danger" onclick="deleteSchedule()" id="deleteBtn" style="display: none;">刪除</button>
        </div>
      </form>
    </div>
  </div>

  <!-- 附近行程查詢結果模態框 -->
  <div class="modal" id="nearbyModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="nearbyModalTitle">附近行程查詢結果</h2>
        <button class="close-btn" onclick="closeNearbyModal()">&times;</button>
      </div>
      <div id="nearbyResults"></div>
      <div class="btn-group" style="margin-top: 20px;">
        <button type="button" class="btn-primary" onclick="insertSelectedSchedules()">插入</button>
        <button type="button" class="btn-secondary" onclick="closeNearbyModal()">關閉</button>
      </div>
    </div>
  </div>

  <script>
    // 應用程式狀態
    let currentDate = new Date();
    let monthSchedules = {};
    let allCities = [];
    let selectedScheduleId = null;

    // DOM 元素
    const monthYear = document.getElementById('monthYear');
    const calendarGrid = document.querySelector('.calendar-grid');
    const scheduleModal = document.getElementById('scheduleModal');
    const scheduleForm = document.getElementById('scheduleForm');
    const scheduleCity = document.getElementById('scheduleCity');
    const scheduleDistrict = document.getElementById('scheduleDistrict');
    const inputCity = document.getElementById('inputCity');
    const inputDistrict = document.getElementById('inputDistrict');
    const scheduleDate = document.getElementById('scheduleDate');
    const modalTitle = document.getElementById('modalTitle');
    const scheduleTitle = document.getElementById('scheduleTitle');
    const scheduleNotes = document.getElementById('scheduleNotes');
    const deleteBtn = document.getElementById('deleteBtn');

    // 初始化
    document.addEventListener('DOMContentLoaded', async function() {
      await loadCities();
      await renderCalendar();
      initializeEventListeners();
    });

    // 載入城市資料
    async function loadCities() {
      try {
        const response = await fetch('/api/cities');
        allCities = await response.json();

        const cityOptions = allCities.map(city => \`<option value="\${city.id}">\${city.name}</option>\`).join('');
        scheduleCity.innerHTML = '<option value="">請選擇縣市</option>' + cityOptions;
        inputCity.innerHTML = '<option value="">請選擇縣市</option>' + cityOptions;
      } catch (error) {
        console.error('Failed to load cities:', error);
      }
    }

    // 渲染日曆
    async function renderCalendar() {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth();

      monthYear.textContent = \`\${year}年 \${month + 1}月\`;

      // 計算日曆顯示範圍
      const firstDay = new Date(year, month, 1);
      const startDate = new Date(firstDay);
      startDate.setDate(startDate.getDate() - firstDay.getDay());

      // 清除舊的日期格子（保留星期標題）
      const dayHeaders = calendarGrid.querySelectorAll('.calendar-day-header');
      calendarGrid.innerHTML = '';
      dayHeaders.forEach(header => calendarGrid.appendChild(header));

      // 載入該月份的日程資料
      await loadMonthSchedules(year, month);

      // 生成42天的日曆格子
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);

        const dayElement = createDayElement(date, month);
        calendarGrid.appendChild(dayElement);
      }
    }

    // 建立日期格子
    function createDayElement(date, currentMonth) {
      const day = document.createElement('div');
      day.className = 'calendar-day';

      const clickDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      day.onclick = () => openScheduleModal(clickDate);

      if (date.getMonth() !== currentMonth) {
        day.classList.add('other-month');
      }

      if (isToday(date)) {
        day.classList.add('today');
      }

      const dateStr = formatDate(date);
      const schedule = monthSchedules[dateStr];

      if (schedule) {
        day.classList.add('has-schedule');
      }

      day.innerHTML = \`
        <div class="day-number">\${date.getDate()}</div>
        \${schedule ? \`
          <div class="day-region">\${schedule.city_name || ''}\${schedule.district_name ? ' - ' + schedule.district_name : ''}</div>
          <div class="day-title">\${schedule.title || ''}</div>
        \` : ''}
      \`;

      return day;
    }

    // 載入月份日程
    async function loadMonthSchedules(year, month) {
      try {
        const startDate = \`\${year}-\${String(month + 1).padStart(2, '0')}-01\`;
        const endDate = \`\${year}-\${String(month + 2).padStart(2, '0')}-01\`;

        const url = \`/api/schedules?start=\${startDate}&end=\${endDate}\`;
        const response = await fetch(url);
        const schedules = await response.json();

        monthSchedules = {};
        schedules.forEach(schedule => {
          monthSchedules[schedule.date] = schedule;
        });
      } catch (error) {
        console.error('Failed to load schedules:', error);
        monthSchedules = {};
      }
    }

    // 工具函數
    function formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return \`\${year}-\${month}-\${day}\`;
    }

    function isToday(date) {
      const today = new Date();
      return date.toDateString() === today.toDateString();
    }

    // 日曆導航
    async function previousMonth() {
      currentDate.setMonth(currentDate.getMonth() - 1);
      await renderCalendar();
    }

    async function nextMonth() {
      currentDate.setMonth(currentDate.getMonth() + 1);
      await renderCalendar();
    }

    async function goToToday() {
      currentDate = new Date();
      await renderCalendar();
    }

    // 模態框操作
    function openScheduleModal(date) {
      const dateStr = formatDate(date);
      const schedule = monthSchedules[dateStr];

      scheduleDate.value = dateStr;
      modalTitle.textContent = \`\${date.getFullYear()}年\${date.getMonth() + 1}月\${date.getDate()}日 行程\`;

      if (schedule) {
        selectedScheduleId = schedule.id;
        scheduleCity.value = schedule.city_id || '';
        scheduleTitle.value = schedule.title || '';
        scheduleNotes.value = schedule.notes || '';
        deleteBtn.style.display = 'block';

        if (schedule.city_id) {
          loadDistrictsForSchedule(schedule.city_id).then(() => {
            scheduleDistrict.value = schedule.district_id || '';
          });
        }
      } else {
        selectedScheduleId = null;
        scheduleForm.reset();
        scheduleDate.value = dateStr;
        deleteBtn.style.display = 'none';
      }

      scheduleModal.classList.add('active');
    }

    function closeModal() {
      scheduleModal.classList.remove('active');
    }

    // 載入區域資料
    async function loadDistrictsForSchedule(cityId) {
      try {
        const response = await fetch(\`/api/cities/\${cityId}/districts\`);
        const districts = await response.json();

        scheduleDistrict.innerHTML = '<option value="">請選擇區域（可選）</option>' +
          districts.map(district => \`<option value="\${district.id}">\${district.name}</option>\`).join('');
      } catch (error) {
        console.error('Failed to load districts:', error);
      }
    }

    // 事件監聽器
    function initializeEventListeners() {
      scheduleCity.addEventListener('change', function(e) {
        const cityId = e.target.value;
        if (cityId) {
          loadDistrictsForSchedule(cityId);
        } else {
          scheduleDistrict.innerHTML = '<option value="">請選擇區域（可選）</option>';
        }
      });

      scheduleForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const dateValue = scheduleDate.value;
        const title = scheduleTitle.value.trim();

        if (!dateValue) {
          alert('請選擇日期');
          return;
        }

        if (!title) {
          alert('請輸入行程標題');
          return;
        }

        try {
          const method = selectedScheduleId ? 'PUT' : 'POST';
          const url = selectedScheduleId ? \`/api/schedules/\${selectedScheduleId}\` : '/api/schedules';

          const scheduleData = {
            date: dateValue,
            city_id: scheduleCity.value || null,
            district_id: scheduleDistrict.value || null,
            title: title,
            notes: scheduleNotes.value.trim()
          };

          const response = await fetch(url, {
            method: method,
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(scheduleData)
          });

          if (response.ok) {
            alert(selectedScheduleId ? '日程已更新' : '日程已創建');
            closeModal();
            await renderCalendar();
          } else {
            throw new Error('儲存失敗');
          }
        } catch (error) {
          console.error('Failed to save schedule:', error);
          alert('儲存失敗，請稍後再試');
        }
      });
    }

    // 刪除日程
    async function deleteSchedule() {
      if (!selectedScheduleId) return;

      if (confirm('確定要刪除這個日程嗎？')) {
        try {
          const response = await fetch(\`/api/schedules/\${selectedScheduleId}\`, {
            method: 'DELETE'
          });

          if (response.ok) {
            alert('日程已刪除');
            closeModal();
            await renderCalendar();
          }
        } catch (error) {
          console.error('Failed to delete schedule:', error);
          alert('刪除失敗，請稍後再試');
        }
      }
    }

    // 附近行程查詢（簡化版本）
    function findNearestSchedules() {
      alert('附近行程查詢功能將在後續版本中實現');
    }

    function closeNearbyModal() {
      // 暫時空實現
    }

    function insertSelectedSchedules() {
      // 暫時空實現
    }

    function addScheduleItem() {
      // 暫時空實現
    }

    console.log('SGS排行程 - 重構版本載入完成');
  </script>
</body>
</html>`;
    
    return c.html(htmlContent);
  } catch (error) {
    console.error('Error serving homepage:', error);
    return c.html('<h1>Error loading page</h1>', 500);
  }
});

export default app;
