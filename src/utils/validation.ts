/**
 * 輸入驗證模組
 * 提供統一的資料驗證功能
 */

import { ValidationError, createValidationError } from './errors';

// 基本驗證函數
export const isRequired = (value: any, fieldName: string) => {
  if (value === null || value === undefined || value === '') {
    throw createValidationError(fieldName, value, 'is required');
  }
  return true;
};

export const isString = (value: any, fieldName: string) => {
  if (typeof value !== 'string') {
    throw createValidationError(fieldName, value, 'must be a string');
  }
  return true;
};

export const isNumber = (value: any, fieldName: string) => {
  if (typeof value !== 'number' || isNaN(value)) {
    throw createValidationError(fieldName, value, 'must be a valid number');
  }
  return true;
};

export const isInteger = (value: any, fieldName: string) => {
  if (!Number.isInteger(Number(value))) {
    throw createValidationError(fieldName, value, 'must be an integer');
  }
  return true;
};

export const isDate = (value: any, fieldName: string) => {
  const date = new Date(value);
  if (isNaN(date.getTime())) {
    throw createValidationError(fieldName, value, 'must be a valid date');
  }
  return true;
};

export const maxLength = (value: string, max: number, fieldName: string) => {
  if (value && value.length > max) {
    throw createValidationError(fieldName, value, `must not exceed ${max} characters`);
  }
  return true;
};

export const minLength = (value: string, min: number, fieldName: string) => {
  if (value && value.length < min) {
    throw createValidationError(fieldName, value, `must be at least ${min} characters`);
  }
  return true;
};

export const isPositive = (value: number, fieldName: string) => {
  if (value <= 0) {
    throw createValidationError(fieldName, value, 'must be positive');
  }
  return true;
};

export const isLatitude = (value: number, fieldName: string) => {
  if (value < -90 || value > 90) {
    throw createValidationError(fieldName, value, 'must be between -90 and 90');
  }
  return true;
};

export const isLongitude = (value: number, fieldName: string) => {
  if (value < -180 || value > 180) {
    throw createValidationError(fieldName, value, 'must be between -180 and 180');
  }
  return true;
};

// 複合驗證器
export interface ValidationRule {
  field: string;
  value: any;
  rules: Array<(value: any, fieldName: string) => boolean>;
}

export const validateFields = (rules: ValidationRule[]) => {
  const errors: any[] = [];
  
  for (const rule of rules) {
    try {
      for (const validationFn of rule.rules) {
        validationFn(rule.value, rule.field);
      }
    } catch (error) {
      if (error instanceof ValidationError) {
        errors.push(error.details);
      }
    }
  }
  
  if (errors.length > 0) {
    throw new ValidationError('Validation failed', { errors });
  }
  
  return true;
};

// 特定業務邏輯驗證
export const validateScheduleData = (data: any) => {
  const rules: ValidationRule[] = [
    {
      field: 'date',
      value: data.date,
      rules: [isRequired, isDate]
    },
    {
      field: 'title',
      value: data.title,
      rules: [isRequired, isString, (v, f) => maxLength(v, 100, f)]
    }
  ];

  // 可選欄位驗證
  if (data.city_id !== null && data.city_id !== undefined) {
    rules.push({
      field: 'city_id',
      value: data.city_id,
      rules: [isInteger, (v, f) => isPositive(Number(v), f)]
    });
  }

  if (data.district_id !== null && data.district_id !== undefined) {
    rules.push({
      field: 'district_id',
      value: data.district_id,
      rules: [isInteger, (v, f) => isPositive(Number(v), f)]
    });
  }

  if (data.notes) {
    rules.push({
      field: 'notes',
      value: data.notes,
      rules: [isString, (v, f) => maxLength(v, 1000, f)]
    });
  }

  return validateFields(rules);
};

export const validateScheduleItemData = (data: any) => {
  const rules: ValidationRule[] = [
    {
      field: 'location',
      value: data.location,
      rules: [isRequired, isString, (v, f) => maxLength(v, 200, f)]
    },
    {
      field: 'activity',
      value: data.activity,
      rules: [isRequired, isString, (v, f) => maxLength(v, 500, f)]
    }
  ];

  // 可選欄位驗證
  if (data.time) {
    rules.push({
      field: 'time',
      value: data.time,
      rules: [isString, (v, f) => maxLength(v, 10, f)]
    });
  }

  if (data.notes) {
    rules.push({
      field: 'notes',
      value: data.notes,
      rules: [isString, (v, f) => maxLength(v, 500, f)]
    });
  }

  if (data.latitude !== null && data.latitude !== undefined) {
    rules.push({
      field: 'latitude',
      value: data.latitude,
      rules: [isNumber, isLatitude]
    });
  }

  if (data.longitude !== null && data.longitude !== undefined) {
    rules.push({
      field: 'longitude',
      value: data.longitude,
      rules: [isNumber, isLongitude]
    });
  }

  if (data.order_index !== null && data.order_index !== undefined) {
    rules.push({
      field: 'order_index',
      value: data.order_index,
      rules: [isInteger]
    });
  }

  return validateFields(rules);
};
