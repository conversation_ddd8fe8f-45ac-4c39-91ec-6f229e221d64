/**
 * 統一錯誤處理模組
 * 提供標準化的錯誤類型和處理機制
 */

import { Context } from 'hono';

// 自定義錯誤類型
export class ApiError extends Error {
  constructor(
    public status: number,
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, details?: any) {
    super(400, message, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends ApiError {
  constructor(resource: string) {
    super(404, `${resource} not found`, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

export class DatabaseError extends ApiError {
  constructor(message: string, details?: any) {
    super(500, 'Database operation failed', 'DATABASE_ERROR', { originalMessage: message, ...details });
    this.name = 'DatabaseError';
  }
}

// 錯誤處理中間件
export const errorHandler = (error: Error, c: Context) => {
  // 記錄錯誤
  console.error('Error occurred:', {
    name: error.name,
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    url: c.req.url,
    method: c.req.method
  });

  // 處理已知的 API 錯誤
  if (error instanceof ApiError) {
    return c.json({
      error: {
        message: error.message,
        code: error.code,
        details: error.details
      }
    }, error.status);
  }

  // 處理未知錯誤
  return c.json({
    error: {
      message: 'Internal server error',
      code: 'INTERNAL_ERROR'
    }
  }, 500);
};

// 異步錯誤包裝器
export const asyncHandler = (fn: Function) => {
  return async (c: Context, next?: Function) => {
    try {
      return await fn(c, next);
    } catch (error) {
      return errorHandler(error as Error, c);
    }
  };
};

// 常用錯誤創建函數
export const createValidationError = (field: string, value: any, requirement: string) => {
  return new ValidationError(`Invalid ${field}: ${requirement}`, { field, value });
};

export const createNotFoundError = (resource: string, id?: string | number) => {
  const message = id ? `${resource} with id ${id} not found` : `${resource} not found`;
  return new NotFoundError(message);
};
