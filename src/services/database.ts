/**
 * 資料庫服務模組
 * 提供統一的資料庫操作介面
 */

import { DatabaseError, NotFoundError } from '../utils/errors';

export class DatabaseService {
  constructor(private db: D1Database) {}

  // 通用查詢方法
  async query<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    try {
      const result = await this.db.prepare(sql).bind(...params).all();
      return result.results as T[];
    } catch (error) {
      throw new DatabaseError(`Query failed: ${sql}`, { params, error: (error as Error).message });
    }
  }

  // 通用單筆查詢
  async queryFirst<T = any>(sql: string, params: any[] = []): Promise<T | null> {
    try {
      const result = await this.db.prepare(sql).bind(...params).first();
      return result as T | null;
    } catch (error) {
      throw new DatabaseError(`Query failed: ${sql}`, { params, error: (error as Error).message });
    }
  }

  // 通用插入方法
  async insert(sql: string, params: any[] = []): Promise<number> {
    try {
      const result = await this.db.prepare(sql).bind(...params).run();
      if (!result.success) {
        throw new Error('Insert operation failed');
      }
      return result.meta.last_row_id as number;
    } catch (error) {
      throw new DatabaseError(`Insert failed: ${sql}`, { params, error: (error as Error).message });
    }
  }

  // 通用更新方法
  async update(sql: string, params: any[] = []): Promise<number> {
    try {
      const result = await this.db.prepare(sql).bind(...params).run();
      if (!result.success) {
        throw new Error('Update operation failed');
      }
      return result.meta.changes as number;
    } catch (error) {
      throw new DatabaseError(`Update failed: ${sql}`, { params, error: (error as Error).message });
    }
  }

  // 通用刪除方法
  async delete(sql: string, params: any[] = []): Promise<number> {
    try {
      const result = await this.db.prepare(sql).bind(...params).run();
      if (!result.success) {
        throw new Error('Delete operation failed');
      }
      return result.meta.changes as number;
    } catch (error) {
      throw new DatabaseError(`Delete failed: ${sql}`, { params, error: (error as Error).message });
    }
  }

  // 檢查記錄是否存在
  async exists(table: string, field: string, value: any): Promise<boolean> {
    const sql = `SELECT 1 FROM ${table} WHERE ${field} = ? LIMIT 1`;
    const result = await this.queryFirst(sql, [value]);
    return result !== null;
  }

  // 獲取記錄或拋出 NotFound 錯誤
  async getOrThrow<T>(sql: string, params: any[], resourceName: string): Promise<T> {
    const result = await this.queryFirst<T>(sql, params);
    if (!result) {
      throw new NotFoundError(resourceName);
    }
    return result;
  }
}

// 城市相關資料庫操作
export class CityService extends DatabaseService {
  async getAllCities() {
    return this.query(`
      SELECT * FROM cities 
      ORDER BY sort_order, name
    `);
  }

  async getCityById(id: number) {
    return this.getOrThrow(
      'SELECT * FROM cities WHERE id = ?',
      [id],
      `City with id ${id}`
    );
  }

  async getDistrictsByCity(cityId: number) {
    // 先檢查城市是否存在
    await this.getCityById(cityId);
    
    return this.query(`
      SELECT * FROM districts 
      WHERE city_id = ? 
      ORDER BY sort_order, name
    `, [cityId]);
  }

  async getDistrictById(id: number) {
    return this.getOrThrow(
      'SELECT * FROM districts WHERE id = ?',
      [id],
      `District with id ${id}`
    );
  }

  async getDistrictCoordinates(districtId: number) {
    const district = await this.getOrThrow(
      'SELECT latitude, longitude FROM districts WHERE id = ?',
      [districtId],
      `District coordinates for id ${districtId}`
    );

    return {
      lat: parseFloat(district.latitude),
      lng: parseFloat(district.longitude)
    };
  }
}

// 日程相關資料庫操作
export class ScheduleService extends DatabaseService {
  async getSchedules(filters: {
    start?: string;
    end?: string;
    city_id?: number;
    district_id?: number;
  } = {}) {
    let sql = `
      SELECT ds.*, c.name as city_name, d.name as district_name
      FROM daily_schedules ds
      LEFT JOIN cities c ON ds.city_id = c.id
      LEFT JOIN districts d ON ds.district_id = d.id
      WHERE 1=1
    `;
    
    const params: any[] = [];

    if (filters.start) {
      sql += ' AND ds.date >= ?';
      params.push(filters.start);
    }

    if (filters.end) {
      sql += ' AND ds.date < ?';
      params.push(filters.end);
    }

    if (filters.city_id) {
      sql += ' AND ds.city_id = ?';
      params.push(filters.city_id);
    }

    if (filters.district_id) {
      sql += ' AND ds.district_id = ?';
      params.push(filters.district_id);
    }

    sql += ' ORDER BY ds.date';

    return this.query(sql, params);
  }

  async getScheduleById(id: number) {
    return this.getOrThrow(
      'SELECT * FROM daily_schedules WHERE id = ?',
      [id],
      `Schedule with id ${id}`
    );
  }

  async createSchedule(data: {
    date: string;
    city_id?: number;
    district_id?: number;
    title: string;
    notes?: string;
  }) {
    const sql = `
      INSERT INTO daily_schedules (date, city_id, district_id, title, notes)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    return this.insert(sql, [
      data.date,
      data.city_id || null,
      data.district_id || null,
      data.title,
      data.notes || null
    ]);
  }

  async updateSchedule(id: number, data: {
    date: string;
    city_id?: number;
    district_id?: number;
    title: string;
    notes?: string;
  }) {
    // 先檢查記錄是否存在
    await this.getScheduleById(id);

    const sql = `
      UPDATE daily_schedules
      SET date = ?, city_id = ?, district_id = ?, title = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const changes = await this.update(sql, [
      data.date,
      data.city_id || null,
      data.district_id || null,
      data.title,
      data.notes || null,
      id
    ]);

    if (changes === 0) {
      throw new NotFoundError(`Schedule with id ${id}`);
    }

    return changes;
  }

  async deleteSchedule(id: number) {
    const changes = await this.delete(
      'DELETE FROM daily_schedules WHERE id = ?',
      [id]
    );

    if (changes === 0) {
      throw new NotFoundError(`Schedule with id ${id}`);
    }

    return changes;
  }

  async getScheduleItems(scheduleId: number) {
    // 先檢查日程是否存在
    await this.getScheduleById(scheduleId);

    return this.query(`
      SELECT * FROM schedule_items 
      WHERE daily_schedule_id = ? 
      ORDER BY order_index, time
    `, [scheduleId]);
  }

  async createScheduleItem(scheduleId: number, data: {
    time?: string;
    location: string;
    activity: string;
    notes?: string;
    latitude?: number;
    longitude?: number;
    order_index?: number;
  }) {
    // 先檢查日程是否存在
    await this.getScheduleById(scheduleId);

    const sql = `
      INSERT INTO schedule_items (daily_schedule_id, time, location, activity, notes, latitude, longitude, order_index)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    return this.insert(sql, [
      scheduleId,
      data.time || null,
      data.location,
      data.activity,
      data.notes || null,
      data.latitude || null,
      data.longitude || null,
      data.order_index || 0
    ]);
  }
}
