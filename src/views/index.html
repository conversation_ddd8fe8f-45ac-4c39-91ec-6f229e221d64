<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SGS排行程</title>
  <link rel="stylesheet" href="/static/css/styles.css">
</head>
<body>
  <div class="header">
    <h1>🗓️ SGS排行程</h1>
    <div class="distance-section">
      <div class="filter-group">
        <label>輸入地區查詢附近行程：</label>
        <select id="inputCity">
          <option value="">請選擇縣市</option>
        </select>
      </div>
      <div class="filter-group">
        <select id="inputDistrict">
          <option value="">請先選擇縣市</option>
        </select>
      </div>
      <button class="nav-btn" onclick="findNearestSchedules()">找出最近行程</button>
    </div>
  </div>

  <div class="calendar-container">
    <div class="calendar-header">
      <div class="calendar-nav">
        <button class="nav-btn" onclick="previousMonth()">← 上月</button>
        <button class="nav-btn" onclick="nextMonth()">下月 →</button>
      </div>
      <div class="month-year" id="monthYear"></div>
      <button class="nav-btn" onclick="goToToday()">今天</button>
    </div>

    <div class="calendar-grid">
      <div class="calendar-day-header">日</div>
      <div class="calendar-day-header">一</div>
      <div class="calendar-day-header">二</div>
      <div class="calendar-day-header">三</div>
      <div class="calendar-day-header">四</div>
      <div class="calendar-day-header">五</div>
      <div class="calendar-day-header">六</div>
    </div>
  </div>

  <!-- 日程編輯模態框 -->
  <div class="modal" id="scheduleModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="modalTitle">編輯日程</h2>
        <button class="close-btn" onclick="closeModal()">&times;</button>
      </div>

      <form id="scheduleForm">
        <div class="form-group">
          <label>日期</label>
          <input type="date" id="scheduleDate" readonly>
        </div>

        <div class="form-group">
          <label>主要縣市</label>
          <select id="scheduleCity">
            <option value="">請選擇縣市</option>
          </select>
        </div>

        <div class="form-group">
          <label>主要區域</label>
          <select id="scheduleDistrict">
            <option value="">請選擇區域（可選）</option>
          </select>
        </div>

        <div class="form-group">
          <label>日程標題</label>
          <input type="text" id="scheduleTitle" placeholder="例如：台北一日遊">
        </div>

        <div class="form-group">
          <label>備註</label>
          <textarea id="scheduleNotes" placeholder="整體行程備註..."></textarea>
        </div>

        <h3>具體行程項目</h3>
        <button type="button" class="add-item-btn" onclick="addScheduleItem()">+ 新增行程項目</button>
        <div id="scheduleItems"></div>

        <div class="btn-group">
          <button type="submit" class="btn-primary">儲存日程</button>
          <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
          <button type="button" class="btn-danger" onclick="deleteSchedule()" id="deleteBtn" style="display: none;">刪除</button>
        </div>
      </form>
    </div>
  </div>

  <!-- 附近行程查詢結果模態框 -->
  <div class="modal" id="nearbyModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="nearbyModalTitle">附近行程查詢結果</h2>
        <button class="close-btn" onclick="closeNearbyModal()">&times;</button>
      </div>

      <div id="nearbyResults">
        <!-- 動態內容 -->
      </div>

      <div class="btn-group" style="margin-top: 20px;">
        <button type="button" class="btn-primary" onclick="insertSelectedSchedules()">插入</button>
        <button type="button" class="btn-secondary" onclick="closeNearbyModal()">關閉</button>
      </div>
    </div>
  </div>

  <!-- 載入 JavaScript -->
  <script src="/static/js/constants.js"></script>
  <script src="/static/js/utils.js"></script>
  <script src="/static/js/api.js"></script>
  <script src="/static/js/calendar.js"></script>
  <script src="/static/js/schedule.js"></script>
  <script src="/static/js/nearby.js"></script>
  <script src="/static/js/app.js"></script>
</body>
</html>
