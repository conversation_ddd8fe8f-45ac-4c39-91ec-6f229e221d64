/**
 * 區域相關路由
 */

import { Hono } from 'hono';
import { CityService } from '../services/database';
import { asyncHandler } from '../utils/errors';

type Env = {
  DB: D1Database;
};

export const districtsRouter = new Hono<{ Bindings: Env }>();

// 取得區域座標
districtsRouter.get('/:id/coordinates', asyncHandler(async (c) => {
  const districtId = parseInt(c.req.param('id'));
  
  if (isNaN(districtId)) {
    return c.json({ error: 'Invalid district ID' }, 400);
  }

  const cityService = new CityService(c.env.DB);
  const coordinates = await cityService.getDistrictCoordinates(districtId);
  return c.json(coordinates);
}));
