/**
 * 城市相關路由
 */

import { Hono } from 'hono';
import { CityService } from '../services/database';
import { asyncHandler } from '../utils/errors';

type Env = {
  DB: D1Database;
};

export const citiesRouter = new Hono<{ Bindings: Env }>();

// 取得所有城市
citiesRouter.get('/', asyncHandler(async (c) => {
  const cityService = new CityService(c.env.DB);
  const cities = await cityService.getAllCities();
  return c.json(cities);
}));

// 取得特定城市的所有區域
citiesRouter.get('/:id/districts', asyncHandler(async (c) => {
  const cityId = parseInt(c.req.param('id'));
  
  if (isNaN(cityId)) {
    return c.json({ error: 'Invalid city ID' }, 400);
  }

  const cityService = new CityService(c.env.DB);
  const districts = await cityService.getDistrictsByCity(cityId);
  return c.json(districts);
}));
