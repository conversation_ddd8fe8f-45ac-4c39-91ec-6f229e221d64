/**
 * 日程相關路由
 */

import { Hono } from 'hono';
import { ScheduleService } from '../services/database';
import { asyncHandler } from '../utils/errors';
import { validateScheduleData, validateScheduleItemData } from '../utils/validation';

type Env = {
  DB: D1Database;
};

export const schedulesRouter = new Hono<{ Bindings: Env }>();

// 取得日程列表（支援篩選）
schedulesRouter.get('/', asyncHandler(async (c) => {
  const { start, end, city_id, district_id } = c.req.query();
  
  const filters: any = {};
  
  if (start) filters.start = start;
  if (end) filters.end = end;
  if (city_id) {
    const cityIdNum = parseInt(city_id);
    if (isNaN(cityIdNum)) {
      return c.json({ error: 'Invalid city_id' }, 400);
    }
    filters.city_id = cityIdNum;
  }
  if (district_id) {
    const districtIdNum = parseInt(district_id);
    if (isNaN(districtIdNum)) {
      return c.json({ error: 'Invalid district_id' }, 400);
    }
    filters.district_id = districtIdNum;
  }

  const scheduleService = new ScheduleService(c.env.DB);
  const schedules = await scheduleService.getSchedules(filters);
  return c.json(schedules);
}));

// 取得特定日程
schedulesRouter.get('/:id', asyncHandler(async (c) => {
  const id = parseInt(c.req.param('id'));
  
  if (isNaN(id)) {
    return c.json({ error: 'Invalid schedule ID' }, 400);
  }

  const scheduleService = new ScheduleService(c.env.DB);
  const schedule = await scheduleService.getScheduleById(id);
  return c.json(schedule);
}));

// 新增日程
schedulesRouter.post('/', asyncHandler(async (c) => {
  const body = await c.req.json();
  
  // 驗證輸入資料
  validateScheduleData(body);
  
  const scheduleService = new ScheduleService(c.env.DB);
  const id = await scheduleService.createSchedule(body);
  
  return c.json({ id, ...body }, 201);
}));

// 更新日程
schedulesRouter.put('/:id', asyncHandler(async (c) => {
  const id = parseInt(c.req.param('id'));
  
  if (isNaN(id)) {
    return c.json({ error: 'Invalid schedule ID' }, 400);
  }

  const body = await c.req.json();
  
  // 驗證輸入資料
  validateScheduleData(body);
  
  const scheduleService = new ScheduleService(c.env.DB);
  await scheduleService.updateSchedule(id, body);
  
  return c.json({ id, ...body });
}));

// 刪除日程
schedulesRouter.delete('/:id', asyncHandler(async (c) => {
  const id = parseInt(c.req.param('id'));
  
  if (isNaN(id)) {
    return c.json({ error: 'Invalid schedule ID' }, 400);
  }

  const scheduleService = new ScheduleService(c.env.DB);
  await scheduleService.deleteSchedule(id);
  
  return c.json({ message: 'Schedule deleted successfully' });
}));

// 取得日程項目
schedulesRouter.get('/:id/items', asyncHandler(async (c) => {
  const scheduleId = parseInt(c.req.param('id'));
  
  if (isNaN(scheduleId)) {
    return c.json({ error: 'Invalid schedule ID' }, 400);
  }

  const scheduleService = new ScheduleService(c.env.DB);
  const items = await scheduleService.getScheduleItems(scheduleId);
  return c.json(items);
}));

// 新增日程項目
schedulesRouter.post('/:id/items', asyncHandler(async (c) => {
  const scheduleId = parseInt(c.req.param('id'));
  
  if (isNaN(scheduleId)) {
    return c.json({ error: 'Invalid schedule ID' }, 400);
  }

  const body = await c.req.json();
  
  // 驗證輸入資料
  validateScheduleItemData(body);
  
  const scheduleService = new ScheduleService(c.env.DB);
  const id = await scheduleService.createScheduleItem(scheduleId, body);
  
  return c.json({ id, daily_schedule_id: scheduleId, ...body }, 201);
}));
