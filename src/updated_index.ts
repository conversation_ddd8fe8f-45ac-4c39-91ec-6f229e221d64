import { Hono } from 'hono';
import { cors } from 'hono/cors';

type Env = {
  DB: D1Database;
};

const app = new Hono<{ Bindings: Env }>();

app.use('/*', cors());

app.get('/', (c) => {
  return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>台灣行程管理系統</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; background: #f5f5f5; }
    .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
    header { background: #2563eb; color: white; padding: 20px 0; margin-bottom: 30px; }
    h1 { text-align: center; font-size: 28px; }
    .city-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); gap: 15px; margin-bottom: 30px; }
    .city-card { background: white; padding: 15px; border-radius: 8px; cursor: pointer; transition: all 0.3s; box-shadow: 0 1px 3px rgba(0,0,0,0.1); text-align: center; }
    .city-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
    .city-card h3 { color: #1e40af; margin-bottom: 5px; font-size: 16px; }
    .city-card p { color: #6b7280; font-size: 12px; }
    .districts-section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; display: none; }
    .district-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); gap: 10px; margin-top: 15px; }
    .district-card { background: #f3f4f6; padding: 8px 12px; border-radius: 6px; cursor: pointer; transition: all 0.2s; text-align: center; font-size: 14px; }
    .district-card:hover { background: #e5e7eb; }
    .district-card.selected { background: #dbeafe; color: #1d4ed8; border: 1px solid #3b82f6; }
    .itinerary-section { background: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; display: none; }
    .itinerary-list { margin-top: 20px; }
    .itinerary-item { background: #f9fafb; padding: 15px; margin-bottom: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; }
    .itinerary-item h4 { color: #1f2937; margin-bottom: 8px; }
    .itinerary-item .date { color: #6b7280; font-size: 14px; margin-bottom: 5px; }
    .itinerary-item .location-info { color: #059669; font-size: 14px; margin-bottom: 5px; }
    .itinerary-item .description { color: #4b5563; }
    .item-list { margin-top: 15px; padding-left: 20px; }
    .item { margin-bottom: 10px; padding: 10px; background: white; border-radius: 4px; }
    .item-time { color: #3b82f6; font-weight: 600; margin-right: 10px; }
    .item-location { color: #059669; margin-right: 10px; }
    .item-activity { color: #1f2937; }
    .back-btn { background: #6b7280; color: white; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; margin-right: 10px; }
    .add-btn { background: #10b981; color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; }
    .add-btn:hover { background: #059669; }
    .form-group { margin-bottom: 15px; }
    .form-group label { display: block; margin-bottom: 5px; color: #374151; font-weight: 500; }
    .form-group input, .form-group textarea, .form-group select { width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 4px; font-size: 14px; }
    .form-group textarea { resize: vertical; min-height: 80px; }
    .modal { display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 1000; }
    .modal.active { display: flex; }
    .modal-content { background: white; padding: 30px; border-radius: 8px; width: 90%; max-width: 500px; max-height: 80vh; overflow-y: auto; }
    .modal-header { margin-bottom: 20px; }
    .modal-header h2 { color: #1f2937; }
    .close-btn { float: right; background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; }
    .btn-group { display: flex; gap: 10px; margin-top: 20px; }
    .btn-primary { background: #3b82f6; color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; flex: 1; }
    .btn-secondary { background: #6b7280; color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer; flex: 1; }
  </style>
</head>
<body>
  <header>
    <h1>🇹🇼 台灣行程管理系統</h1>
  </header>
  <div class="container">
    <h2 style="margin-bottom: 20px; color: #1f2937;">選擇縣市</h2>
    <div class="city-grid" id="cityGrid"></div>

    <div class="districts-section" id="districtsSection">
      <button class="back-btn" onclick="backToCities()">← 返回縣市選擇</button>
      <h3 id="cityTitle" style="color: #1f2937; margin-bottom: 10px; display: inline-block; margin-left: 10px;"></h3>
      <div class="district-grid" id="districtGrid"></div>
      <div style="margin-top: 20px;">
        <button class="add-btn" onclick="showAddItineraryModal()">新增行程</button>
      </div>
    </div>

    <div class="itinerary-section" id="itinerarySection">
      <button class="back-btn" onclick="backToDistricts()">← 返回區域選擇</button>
      <h3 id="locationTitle" style="color: #1f2937; margin-bottom: 10px; display: inline-block; margin-left: 10px;"></h3>
      <button class="add-btn" onclick="showAddItineraryModal()" style="margin-left: 15px;">新增行程</button>
      <div class="itinerary-list" id="itineraryList"></div>
    </div>
  </div>

  <div class="modal" id="addItineraryModal">
    <div class="modal-content">
      <div class="modal-header">
        <button class="close-btn" onclick="closeModal()">&times;</button>
        <h2>新增行程</h2>
      </div>
      <form id="itineraryForm">
        <div class="form-group">
          <label>縣市</label>
          <select id="citySelect" required>
            <option value="">請選擇縣市</option>
          </select>
        </div>
        <div class="form-group">
          <label>區域（可選）</label>
          <select id="districtSelect">
            <option value="">請先選擇縣市</option>
          </select>
        </div>
        <div class="form-group">
          <label>行程標題</label>
          <input type="text" id="title" required>
        </div>
        <div class="form-group">
          <label>行程描述</label>
          <textarea id="description"></textarea>
        </div>
        <div class="form-group">
          <label>開始日期</label>
          <input type="date" id="startDate">
        </div>
        <div class="form-group">
          <label>結束日期</label>
          <input type="date" id="endDate">
        </div>
        <div class="form-group">
          <label>天數</label>
          <input type="number" id="dayNumber" min="1">
        </div>
        <div class="btn-group">
          <button type="submit" class="btn-primary">確認新增</button>
          <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    let currentCity = null;
    let currentDistrict = null;
    let allCities = [];
    let currentDistricts = [];

    async function loadCities() {
      try {
        const response = await fetch('/api/cities');
        allCities = await response.json();
        const grid = document.getElementById('cityGrid');
        grid.innerHTML = allCities.map(city => \`
          <div class="city-card" onclick="selectCity(\${city.id}, '\${city.name}')">
            <h3>\${city.name}</h3>
            <p>點擊查看區域</p>
          </div>
        \`).join('');

        // 填充縣市選單
        const citySelect = document.getElementById('citySelect');
        citySelect.innerHTML = '<option value="">請選擇縣市</option>' +
          allCities.map(city => \`<option value="\${city.id}">\${city.name}</option>\`).join('');
      } catch (error) {
        console.error('Failed to load cities:', error);
      }
    }

    async function selectCity(cityId, cityName) {
      currentCity = cityId;
      currentDistrict = null;
      document.getElementById('cityTitle').textContent = cityName;
      document.getElementById('districtsSection').style.display = 'block';
      document.getElementById('itinerarySection').style.display = 'none';
      await loadDistricts(cityId);
    }

    async function loadDistricts(cityId) {
      try {
        const response = await fetch(\`/api/cities/\${cityId}/districts\`);
        currentDistricts = await response.json();
        const grid = document.getElementById('districtGrid');
        grid.innerHTML = currentDistricts.map(district => \`
          <div class="district-card" onclick="selectDistrict(\${district.id}, '\${district.name}')">
            \${district.name}
          </div>
        \`).join('');
      } catch (error) {
        console.error('Failed to load districts:', error);
      }
    }

    async function selectDistrict(districtId, districtName) {
      currentDistrict = districtId;
      const cityName = document.getElementById('cityTitle').textContent;
      document.getElementById('locationTitle').textContent = \`\${cityName} > \${districtName}\`;
      document.getElementById('itinerarySection').style.display = 'block';

      // 更新選中狀態
      document.querySelectorAll('.district-card').forEach(card => card.classList.remove('selected'));
      event.target.classList.add('selected');

      await loadItineraries();
    }

    async function loadItineraries() {
      try {
        const endpoint = currentDistrict
          ? \`/api/districts/\${currentDistrict}/itineraries\`
          : \`/api/cities/\${currentCity}/itineraries\`;

        const response = await fetch(endpoint);
        const itineraries = await response.json();
        const list = document.getElementById('itineraryList');

        if (itineraries.length === 0) {
          list.innerHTML = '<p style="color: #6b7280; text-align: center; padding: 20px;">尚無行程，請新增行程</p>';
          return;
        }

        list.innerHTML = itineraries.map(itinerary => \`
          <div class="itinerary-item">
            <h4>\${itinerary.title}</h4>
            \${itinerary.start_date ? \`<div class="date">📅 \${itinerary.start_date} ~ \${itinerary.end_date || itinerary.start_date}</div>\` : ''}
            \${itinerary.day_number ? \`<div class="date">🎯 第 \${itinerary.day_number} 天</div>\` : ''}
            \${itinerary.city_name && itinerary.district_name ? \`<div class="location-info">📍 \${itinerary.city_name} - \${itinerary.district_name}</div>\` : ''}
            \${itinerary.description ? \`<div class="description">\${itinerary.description}</div>\` : ''}
            <button onclick="loadItineraryDetails(\${itinerary.id})" style="margin-top: 10px; padding: 5px 10px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">查看詳情</button>
          </div>
        \`).join('');
      } catch (error) {
        console.error('Failed to load itineraries:', error);
      }
    }

    async function loadItineraryDetails(itineraryId) {
      try {
        const response = await fetch(\`/api/itineraries/\${itineraryId}\`);
        const data = await response.json();

        if (data.items && data.items.length > 0) {
          const itemsHtml = data.items.map(item => \`
            <div class="item">
              \${item.time ? \`<span class="item-time">\${item.time}</span>\` : ''}
              <span class="item-location">📍 \${item.location}</span>
              <span class="item-activity">\${item.activity}</span>
              \${item.notes ? \`<div style="margin-top: 5px; color: #6b7280; font-size: 14px;">\${item.notes}</div>\` : ''}
            </div>
          \`).join('');

          const itineraryElement = document.querySelector(\`.itinerary-item:has(button[onclick="loadItineraryDetails(\${itineraryId})"])\`);
          const existingItems = itineraryElement.querySelector('.item-list');
          if (existingItems) {
            existingItems.remove();
          } else {
            itineraryElement.insertAdjacentHTML('beforeend', \`<div class="item-list">\${itemsHtml}</div>\`);
          }
        }
      } catch (error) {
        console.error('Failed to load itinerary details:', error);
      }
    }

    function backToCities() {
      document.getElementById('districtsSection').style.display = 'none';
      document.getElementById('itinerarySection').style.display = 'none';
      currentCity = null;
      currentDistrict = null;
    }

    function backToDistricts() {
      document.getElementById('itinerarySection').style.display = 'none';
      currentDistrict = null;
      document.querySelectorAll('.district-card').forEach(card => card.classList.remove('selected'));
    }

    function showAddItineraryModal() {
      document.getElementById('addItineraryModal').classList.add('active');

      // 預選城市
      if (currentCity) {
        document.getElementById('citySelect').value = currentCity;
        loadDistrictsForSelect(currentCity);
      }
      if (currentDistrict) {
        setTimeout(() => {
          document.getElementById('districtSelect').value = currentDistrict;
        }, 100);
      }
    }

    async function loadDistrictsForSelect(cityId) {
      try {
        const response = await fetch(\`/api/cities/\${cityId}/districts\`);
        const districts = await response.json();
        const districtSelect = document.getElementById('districtSelect');
        districtSelect.innerHTML = '<option value="">請選擇區域（可選）</option>' +
          districts.map(district => \`<option value="\${district.id}">\${district.name}</option>\`).join('');
      } catch (error) {
        console.error('Failed to load districts for select:', error);
      }
    }

    function closeModal() {
      document.getElementById('addItineraryModal').classList.remove('active');
      document.getElementById('itineraryForm').reset();
    }

    // 縣市選擇變更事件
    document.getElementById('citySelect').addEventListener('change', function(e) {
      const cityId = e.target.value;
      if (cityId) {
        loadDistrictsForSelect(cityId);
      } else {
        document.getElementById('districtSelect').innerHTML = '<option value="">請先選擇縣市</option>';
      }
    });

    document.getElementById('itineraryForm').addEventListener('submit', async (e) => {
      e.preventDefault();

      const data = {
        city_id: document.getElementById('citySelect').value,
        district_id: document.getElementById('districtSelect').value || null,
        title: document.getElementById('title').value,
        description: document.getElementById('description').value,
        start_date: document.getElementById('startDate').value || null,
        end_date: document.getElementById('endDate').value || null,
        day_number: document.getElementById('dayNumber').value || null
      };

      try {
        const response = await fetch('/api/itineraries', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });

        if (response.ok) {
          closeModal();
          if (currentDistrict || currentCity) {
            await loadItineraries();
          }
        }
      } catch (error) {
        console.error('Failed to create itinerary:', error);
        alert('新增失敗，請稍後再試');
      }
    });

    // 初始化
    loadCities();
  </script>
</body>
</html>`);
});

// 取得所有城市
app.get('/api/cities', async (c) => {
  try {
    const results = await c.env.DB.prepare('SELECT * FROM cities ORDER BY sort_order, name').all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch cities' }, 500);
  }
});

// 取得特定城市的所有區域
app.get('/api/cities/:id/districts', async (c) => {
  const cityId = c.req.param('id');
  try {
    const results = await c.env.DB.prepare(
      'SELECT * FROM districts WHERE city_id = ? ORDER BY sort_order, name'
    ).bind(cityId).all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch districts' }, 500);
  }
});

// 取得城市的行程
app.get('/api/cities/:id/itineraries', async (c) => {
  const cityId = c.req.param('id');
  try {
    const results = await c.env.DB.prepare(`
      SELECT i.*, c.name as city_name, d.name as district_name
      FROM itineraries i
      LEFT JOIN cities c ON i.city_id = c.id
      LEFT JOIN districts d ON i.district_id = d.id
      WHERE i.city_id = ?
      ORDER BY i.day_number, i.start_date
    `).bind(cityId).all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch itineraries' }, 500);
  }
});

// 取得區域的行程
app.get('/api/districts/:id/itineraries', async (c) => {
  const districtId = c.req.param('id');
  try {
    const results = await c.env.DB.prepare(`
      SELECT i.*, c.name as city_name, d.name as district_name
      FROM itineraries i
      LEFT JOIN cities c ON i.city_id = c.id
      LEFT JOIN districts d ON i.district_id = d.id
      WHERE i.district_id = ?
      ORDER BY i.day_number, i.start_date
    `).bind(districtId).all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch itineraries' }, 500);
  }
});

app.get('/api/itineraries/:id', async (c) => {
  const id = c.req.param('id');
  try {
    const itinerary = await c.env.DB.prepare(
      'SELECT * FROM itineraries WHERE id = ?'
    ).bind(id).first();

    if (!itinerary) {
      return c.json({ error: 'Itinerary not found' }, 404);
    }

    const items = await c.env.DB.prepare(
      'SELECT * FROM itinerary_items WHERE itinerary_id = ? ORDER BY order_index'
    ).bind(id).all();

    return c.json({
      ...itinerary,
      items: items.results
    });
  } catch (error) {
    return c.json({ error: 'Failed to fetch itinerary' }, 500);
  }
});

app.post('/api/itineraries', async (c) => {
  const body = await c.req.json();
  const { city_id, district_id, title, description, start_date, end_date, day_number } = body;

  try {
    const result = await c.env.DB.prepare(
      `INSERT INTO itineraries (city_id, district_id, title, description, start_date, end_date, day_number)
       VALUES (?, ?, ?, ?, ?, ?, ?)`
    ).bind(city_id, district_id || null, title, description, start_date, end_date, day_number).run();

    return c.json({ id: result.meta.last_row_id, ...body }, 201);
  } catch (error) {
    return c.json({ error: 'Failed to create itinerary' }, 500);
  }
});

app.post('/api/itineraries/:id/items', async (c) => {
  const itineraryId = c.req.param('id');
  const body = await c.req.json();
  const { time, location, activity, notes, latitude, longitude, order_index } = body;

  try {
    const result = await c.env.DB.prepare(
      `INSERT INTO itinerary_items (itinerary_id, time, location, activity, notes, latitude, longitude, order_index)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
    ).bind(itineraryId, time, location, activity, notes, latitude || null, longitude || null, order_index || 0).run();

    return c.json({ id: result.meta.last_row_id, itinerary_id: itineraryId, ...body }, 201);
  } catch (error) {
    return c.json({ error: 'Failed to add item' }, 500);
  }
});

app.put('/api/itineraries/:id', async (c) => {
  const id = c.req.param('id');
  const body = await c.req.json();
  const { city_id, district_id, title, description, start_date, end_date, day_number } = body;

  try {
    await c.env.DB.prepare(
      `UPDATE itineraries
       SET city_id = ?, district_id = ?, title = ?, description = ?, start_date = ?, end_date = ?, day_number = ?, updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`
    ).bind(city_id, district_id || null, title, description, start_date, end_date, day_number, id).run();

    return c.json({ id, ...body });
  } catch (error) {
    return c.json({ error: 'Failed to update itinerary' }, 500);
  }
});

app.delete('/api/itineraries/:id', async (c) => {
  const id = c.req.param('id');

  try {
    await c.env.DB.prepare('DELETE FROM itineraries WHERE id = ?').bind(id).run();
    return c.json({ message: 'Itinerary deleted' });
  } catch (error) {
    return c.json({ error: 'Failed to delete itinerary' }, 500);
  }
});

export default app;