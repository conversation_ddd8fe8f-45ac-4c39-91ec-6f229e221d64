import { Hono } from 'hono';
import { cors } from 'hono/cors';

type Env = {
  DB: D1Database;
};

const app = new Hono<{ Bindings: Env }>();

app.use('/*', cors());

// 主頁 - 行事曆介面
app.get('/', (c) => {
  return c.html(`<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>台灣旅遊行事曆</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; background: #f5f5f5; }

    /* 頂部篩選區 */
    .header { background: #2563eb; color: white; padding: 20px; }
    .header h1 { text-align: center; margin-bottom: 20px; }
    .filter-section { display: flex; gap: 15px; align-items: center; justify-content: center; flex-wrap: wrap; }
    .filter-group { display: flex; align-items: center; gap: 8px; }
    .filter-group label { font-weight: 500; }
    .filter-group select { padding: 8px 12px; border: none; border-radius: 6px; min-width: 120px; }

    /* 行事曆區域 */
    .calendar-container { max-width: 1200px; margin: 30px auto; padding: 0 20px; }
    .calendar-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
    .calendar-nav { display: flex; gap: 10px; }
    .nav-btn { background: #3b82f6; color: white; border: none; padding: 10px 15px; border-radius: 6px; cursor: pointer; }
    .nav-btn:hover { background: #2563eb; }
    .month-year { font-size: 24px; font-weight: bold; color: #1f2937; }

    /* 行事曆網格 */
    .calendar-grid { display: grid; grid-template-columns: repeat(7, 1fr); gap: 1px; background: #e5e7eb; border-radius: 8px; overflow: hidden; }
    .calendar-day-header { background: #f3f4f6; padding: 12px; text-align: center; font-weight: 600; color: #374151; }
    .calendar-day { background: white; min-height: 120px; padding: 8px; cursor: pointer; transition: all 0.2s; position: relative; }
    .calendar-day:hover { background: #f9fafb; }
    .calendar-day.other-month { background: #f9fafb; color: #9ca3af; }
    .calendar-day.today { background: #dbeafe; border: 2px solid #3b82f6; }
    .calendar-day.has-schedule { background: #fef3c7; border-left: 4px solid #f59e0b; }
    .calendar-day.filtered-region { background: #dcfce7; border-left: 4px solid #10b981; }

    .day-number { font-weight: 600; margin-bottom: 4px; }
    .day-region { font-size: 12px; color: #059669; font-weight: 500; margin-bottom: 2px; }
    .day-title { font-size: 11px; color: #374151; line-height: 1.2; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; }

    /* 模態框 */
    .modal { display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 1000; }
    .modal.active { display: flex; }
    .modal-content { background: white; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; max-height: 80vh; overflow-y: auto; }
    .modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
    .modal-header h2 { color: #1f2937; }
    .close-btn { background: none; border: none; font-size: 24px; cursor: pointer; color: #6b7280; }

    .form-group { margin-bottom: 15px; }
    .form-group label { display: block; margin-bottom: 5px; color: #374151; font-weight: 500; }
    .form-group input, .form-group textarea, .form-group select { width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
    .form-group textarea { resize: vertical; min-height: 80px; }

    .schedule-items { margin-top: 20px; }
    .schedule-item { background: #f9fafb; padding: 15px; margin-bottom: 10px; border-radius: 6px; border-left: 4px solid #3b82f6; }
    .item-time { color: #3b82f6; font-weight: 600; margin-right: 10px; }
    .item-location { color: #059669; margin-right: 10px; }

    .btn-group { display: flex; gap: 10px; margin-top: 20px; }
    .btn-primary { background: #3b82f6; color: white; padding: 12px 20px; border: none; border-radius: 6px; cursor: pointer; flex: 1; }
    .btn-secondary { background: #6b7280; color: white; padding: 12px 20px; border: none; border-radius: 6px; cursor: pointer; flex: 1; }
    .btn-danger { background: #dc2626; color: white; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; }

    .add-item-btn { background: #10b981; color: white; padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; margin-bottom: 15px; }
  </style>
</head>
<body>
  <div class="header">
    <h1>🗓️ 台灣旅遊行事曆</h1>
    <div class="filter-section">
      <div class="filter-group">
        <label>篩選縣市：</label>
        <select id="cityFilter">
          <option value="">所有地區</option>
        </select>
      </div>
      <div class="filter-group">
        <label>篩選區域：</label>
        <select id="districtFilter">
          <option value="">請先選擇縣市</option>
        </select>
      </div>
      <button class="nav-btn" onclick="clearFilters()">清除篩選</button>
    </div>
  </div>

  <div class="calendar-container">
    <div class="calendar-header">
      <div class="calendar-nav">
        <button class="nav-btn" onclick="previousMonth()">← 上月</button>
        <button class="nav-btn" onclick="nextMonth()">下月 →</button>
      </div>
      <div class="month-year" id="monthYear"></div>
      <button class="nav-btn" onclick="goToToday()">今天</button>
    </div>

    <div class="calendar-grid">
      <div class="calendar-day-header">日</div>
      <div class="calendar-day-header">一</div>
      <div class="calendar-day-header">二</div>
      <div class="calendar-day-header">三</div>
      <div class="calendar-day-header">四</div>
      <div class="calendar-day-header">五</div>
      <div class="calendar-day-header">六</div>
      <!-- 日期格子會由 JS 動態產生 -->
    </div>
  </div>

  <!-- 日程編輯模態框 -->
  <div class="modal" id="scheduleModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="modalTitle">編輯日程</h2>
        <button class="close-btn" onclick="closeModal()">&times;</button>
      </div>

      <form id="scheduleForm">
        <div class="form-group">
          <label>日期</label>
          <input type="date" id="scheduleDate" readonly>
        </div>

        <div class="form-group">
          <label>主要縣市</label>
          <select id="scheduleCity">
            <option value="">請選擇縣市</option>
          </select>
        </div>

        <div class="form-group">
          <label>主要區域</label>
          <select id="scheduleDistrict">
            <option value="">請選擇區域（可選）</option>
          </select>
        </div>

        <div class="form-group">
          <label>日程標題</label>
          <input type="text" id="scheduleTitle" placeholder="例如：台北一日遊">
        </div>

        <div class="form-group">
          <label>備註</label>
          <textarea id="scheduleNotes" placeholder="整體行程備註..."></textarea>
        </div>

        <h3>具體行程項目</h3>
        <button type="button" class="add-item-btn" onclick="addScheduleItem()">+ 新增行程項目</button>
        <div id="scheduleItems"></div>

        <div class="btn-group">
          <button type="submit" class="btn-primary">儲存日程</button>
          <button type="button" class="btn-secondary" onclick="closeModal()">取消</button>
          <button type="button" class="btn-danger" onclick="deleteSchedule()" id="deleteBtn" style="display: none;">刪除</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    let currentDate = new Date();
    let currentScheduleId = null;
    let allCities = [];
    let filteredCity = null;
    let filteredDistrict = null;
    let monthSchedules = {};

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      loadCities();
      renderCalendar();
    });

    // 載入城市資料
    async function loadCities() {
      try {
        const response = await fetch('/api/cities');
        allCities = await response.json();

        // 填充篩選選單
        const cityFilter = document.getElementById('cityFilter');
        const scheduleCity = document.getElementById('scheduleCity');

        const cityOptions = allCities.map(city => \`<option value="\${city.id}">\${city.name}</option>\`).join('');

        cityFilter.innerHTML = '<option value="">所有地區</option>' + cityOptions;
        scheduleCity.innerHTML = '<option value="">請選擇縣市</option>' + cityOptions;
      } catch (error) {
        console.error('Failed to load cities:', error);
      }
    }

    // 渲染日曆
    function renderCalendar() {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth();

      document.getElementById('monthYear').textContent = \`\${year}年 \${month + 1}月\`;

      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const startDate = new Date(firstDay);
      startDate.setDate(startDate.getDate() - firstDay.getDay());

      const grid = document.querySelector('.calendar-grid');
      // 清除舊的日期格子（保留星期標題）
      const dayHeaders = grid.querySelectorAll('.calendar-day-header');
      grid.innerHTML = '';
      dayHeaders.forEach(header => grid.appendChild(header));

      // 載入該月份的日程資料
      loadMonthSchedules(year, month);

      // 生成42天的日曆格子 (6週)
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate);
        date.setDate(startDate.getDate() + i);

        const dayElement = createDayElement(date, month);
        grid.appendChild(dayElement);
      }
    }

    // 建立日期格子
    function createDayElement(date, currentMonth) {
      const day = document.createElement('div');
      day.className = 'calendar-day';
      day.onclick = () => openScheduleModal(date);

      if (date.getMonth() !== currentMonth) {
        day.classList.add('other-month');
      }

      if (isToday(date)) {
        day.classList.add('today');
      }

      const dateStr = formatDate(date);
      const schedule = monthSchedules[dateStr];

      if (schedule) {
        day.classList.add('has-schedule');
        if (filteredCity && schedule.city_id == filteredCity) {
          day.classList.add('filtered-region');
        } else if (filteredDistrict && schedule.district_id == filteredDistrict) {
          day.classList.add('filtered-region');
        }
      }

      day.innerHTML = \`
        <div class="day-number">\${date.getDate()}</div>
        \${schedule ? \`
          <div class="day-region">\${schedule.city_name || ''}\${schedule.district_name ? ' - ' + schedule.district_name : ''}</div>
          <div class="day-title">\${schedule.title || ''}</div>
        \` : ''}
      \`;

      return day;
    }

    // 載入月份日程
    async function loadMonthSchedules(year, month) {
      try {
        const startDate = \`\${year}-\${String(month + 1).padStart(2, '0')}-01\`;
        const endDate = \`\${year}-\${String(month + 2).padStart(2, '0')}-01\`;

        let url = \`/api/schedules?start=\${startDate}&end=\${endDate}\`;
        if (filteredCity) url += \`&city_id=\${filteredCity}\`;
        if (filteredDistrict) url += \`&district_id=\${filteredDistrict}\`;

        const response = await fetch(url);
        const schedules = await response.json();

        monthSchedules = {};
        schedules.forEach(schedule => {
          monthSchedules[schedule.date] = schedule;
        });
      } catch (error) {
        console.error('Failed to load schedules:', error);
        monthSchedules = {};
      }
    }

    // 工具函數
    function formatDate(date) {
      return date.toISOString().split('T')[0];
    }

    function isToday(date) {
      const today = new Date();
      return date.toDateString() === today.toDateString();
    }

    // 日曆導航
    function previousMonth() {
      currentDate.setMonth(currentDate.getMonth() - 1);
      renderCalendar();
    }

    function nextMonth() {
      currentDate.setMonth(currentDate.getMonth() + 1);
      renderCalendar();
    }

    function goToToday() {
      currentDate = new Date();
      renderCalendar();
    }

    // 篩選功能
    document.getElementById('cityFilter').addEventListener('change', function(e) {
      filteredCity = e.target.value || null;
      filteredDistrict = null;
      document.getElementById('districtFilter').innerHTML = '<option value="">請先選擇縣市</option>';

      if (filteredCity) {
        loadDistrictsForFilter(filteredCity);
      }

      renderCalendar();
    });

    async function loadDistrictsForFilter(cityId) {
      try {
        const response = await fetch(\`/api/cities/\${cityId}/districts\`);
        const districts = await response.json();

        const districtFilter = document.getElementById('districtFilter');
        districtFilter.innerHTML = '<option value="">所有區域</option>' +
          districts.map(district => \`<option value="\${district.id}">\${district.name}</option>\`).join('');
      } catch (error) {
        console.error('Failed to load districts:', error);
      }
    }

    document.getElementById('districtFilter').addEventListener('change', function(e) {
      filteredDistrict = e.target.value || null;
      renderCalendar();
    });

    function clearFilters() {
      filteredCity = null;
      filteredDistrict = null;
      document.getElementById('cityFilter').value = '';
      document.getElementById('districtFilter').innerHTML = '<option value="">請先選擇縣市</option>';
      renderCalendar();
    }

    // 開啟日程編輯模態框
    function openScheduleModal(date) {
      const dateStr = formatDate(date);
      const schedule = monthSchedules[dateStr];

      document.getElementById('scheduleDate').value = dateStr;
      document.getElementById('modalTitle').textContent = \`\${date.getFullYear()}年\${date.getMonth() + 1}月\${date.getDate()}日 行程\`;

      if (schedule) {
        // 編輯現有日程
        currentScheduleId = schedule.id;
        document.getElementById('scheduleCity').value = schedule.city_id || '';
        document.getElementById('scheduleTitle').value = schedule.title || '';
        document.getElementById('scheduleNotes').value = schedule.notes || '';
        document.getElementById('deleteBtn').style.display = 'block';

        if (schedule.city_id) {
          loadDistrictsForSchedule(schedule.city_id).then(() => {
            document.getElementById('scheduleDistrict').value = schedule.district_id || '';
          });
        }

        loadScheduleItems(schedule.id);
      } else {
        // 新增日程
        currentScheduleId = null;
        document.getElementById('scheduleForm').reset();
        document.getElementById('scheduleDate').value = dateStr;
        document.getElementById('scheduleItems').innerHTML = '';
        document.getElementById('deleteBtn').style.display = 'none';
      }

      document.getElementById('scheduleModal').classList.add('active');
    }

    function closeModal() {
      document.getElementById('scheduleModal').classList.remove('active');
    }

    // 載入日程項目
    async function loadScheduleItems(scheduleId) {
      try {
        const response = await fetch(\`/api/schedules/\${scheduleId}/items\`);
        const items = await response.json();

        const container = document.getElementById('scheduleItems');
        container.innerHTML = items.map((item, index) => \`
          <div class="schedule-item">
            <span class="item-time">\${item.time || ''}</span>
            <span class="item-location">📍 \${item.location}</span>
            <span>\${item.activity}</span>
            \${item.notes ? \`<div style="margin-top: 5px; color: #6b7280; font-size: 14px;">\${item.notes}</div>\` : ''}
          </div>
        \`).join('');
      } catch (error) {
        console.error('Failed to load schedule items:', error);
      }
    }

    // 縣市變更時載入區域
    document.getElementById('scheduleCity').addEventListener('change', function(e) {
      const cityId = e.target.value;
      if (cityId) {
        loadDistrictsForSchedule(cityId);
      } else {
        document.getElementById('scheduleDistrict').innerHTML = '<option value="">請選擇區域（可選）</option>';
      }
    });

    async function loadDistrictsForSchedule(cityId) {
      try {
        const response = await fetch(\`/api/cities/\${cityId}/districts\`);
        const districts = await response.json();

        const districtSelect = document.getElementById('scheduleDistrict');
        districtSelect.innerHTML = '<option value="">請選擇區域（可選）</option>' +
          districts.map(district => \`<option value="\${district.id}">\${district.name}</option>\`).join('');
      } catch (error) {
        console.error('Failed to load districts:', error);
      }
    }

    // 表單提交
    document.getElementById('scheduleForm').addEventListener('submit', async function(e) {
      e.preventDefault();

      const data = {
        date: document.getElementById('scheduleDate').value,
        city_id: document.getElementById('scheduleCity').value || null,
        district_id: document.getElementById('scheduleDistrict').value || null,
        title: document.getElementById('scheduleTitle').value,
        notes: document.getElementById('scheduleNotes').value
      };

      try {
        const method = currentScheduleId ? 'PUT' : 'POST';
        const url = currentScheduleId ? \`/api/schedules/\${currentScheduleId}\` : '/api/schedules';

        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });

        if (response.ok) {
          closeModal();
          renderCalendar();
        }
      } catch (error) {
        console.error('Failed to save schedule:', error);
        alert('儲存失敗，請稍後再試');
      }
    });

    // 刪除日程
    async function deleteSchedule() {
      if (!currentScheduleId) return;

      if (confirm('確定要刪除這個日程嗎？')) {
        try {
          const response = await fetch(\`/api/schedules/\${currentScheduleId}\`, {
            method: 'DELETE'
          });

          if (response.ok) {
            closeModal();
            renderCalendar();
          }
        } catch (error) {
          console.error('Failed to delete schedule:', error);
          alert('刪除失敗，請稍後再試');
        }
      }
    }
  </script>
</body>
</html>`);
});

// API: 取得所有城市
app.get('/api/cities', async (c) => {
  try {
    const results = await c.env.DB.prepare('SELECT * FROM cities ORDER BY sort_order, name').all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch cities' }, 500);
  }
});

// API: 取得特定城市的所有區域
app.get('/api/cities/:id/districts', async (c) => {
  const cityId = c.req.param('id');
  try {
    const results = await c.env.DB.prepare(
      'SELECT * FROM districts WHERE city_id = ? ORDER BY sort_order, name'
    ).bind(cityId).all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch districts' }, 500);
  }
});

// API: 取得日程（支援日期範圍和地區篩選）
app.get('/api/schedules', async (c) => {
  const { start, end, city_id, district_id } = c.req.query();

  let query = `
    SELECT ds.*, c.name as city_name, d.name as district_name
    FROM daily_schedules ds
    LEFT JOIN cities c ON ds.city_id = c.id
    LEFT JOIN districts d ON ds.district_id = d.id
    WHERE 1=1
  `;

  const params = [];

  if (start) {
    query += ' AND ds.date >= ?';
    params.push(start);
  }

  if (end) {
    query += ' AND ds.date < ?';
    params.push(end);
  }

  if (city_id) {
    query += ' AND ds.city_id = ?';
    params.push(city_id);
  }

  if (district_id) {
    query += ' AND ds.district_id = ?';
    params.push(district_id);
  }

  query += ' ORDER BY ds.date';

  try {
    const results = await c.env.DB.prepare(query).bind(...params).all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch schedules' }, 500);
  }
});

// API: 取得特定日程的項目
app.get('/api/schedules/:id/items', async (c) => {
  const scheduleId = c.req.param('id');
  try {
    const results = await c.env.DB.prepare(
      'SELECT * FROM schedule_items WHERE daily_schedule_id = ? ORDER BY order_index, time'
    ).bind(scheduleId).all();
    return c.json(results.results);
  } catch (error) {
    return c.json({ error: 'Failed to fetch schedule items' }, 500);
  }
});

// API: 新增日程
app.post('/api/schedules', async (c) => {
  const body = await c.req.json();
  const { date, city_id, district_id, title, notes } = body;

  try {
    const result = await c.env.DB.prepare(
      `INSERT INTO daily_schedules (date, city_id, district_id, title, notes)
       VALUES (?, ?, ?, ?, ?)`
    ).bind(date, city_id || null, district_id || null, title, notes).run();

    return c.json({ id: result.meta.last_row_id, ...body }, 201);
  } catch (error) {
    return c.json({ error: 'Failed to create schedule' }, 500);
  }
});

// API: 更新日程
app.put('/api/schedules/:id', async (c) => {
  const id = c.req.param('id');
  const body = await c.req.json();
  const { date, city_id, district_id, title, notes } = body;

  try {
    await c.env.DB.prepare(
      `UPDATE daily_schedules
       SET date = ?, city_id = ?, district_id = ?, title = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`
    ).bind(date, city_id || null, district_id || null, title, notes, id).run();

    return c.json({ id, ...body });
  } catch (error) {
    return c.json({ error: 'Failed to update schedule' }, 500);
  }
});

// API: 刪除日程
app.delete('/api/schedules/:id', async (c) => {
  const id = c.req.param('id');

  try {
    await c.env.DB.prepare('DELETE FROM daily_schedules WHERE id = ?').bind(id).run();
    return c.json({ message: 'Schedule deleted' });
  } catch (error) {
    return c.json({ error: 'Failed to delete schedule' }, 500);
  }
});

// API: 新增日程項目
app.post('/api/schedules/:id/items', async (c) => {
  const scheduleId = c.req.param('id');
  const body = await c.req.json();
  const { time, location, activity, notes, latitude, longitude, order_index } = body;

  try {
    const result = await c.env.DB.prepare(
      `INSERT INTO schedule_items (daily_schedule_id, time, location, activity, notes, latitude, longitude, order_index)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
    ).bind(scheduleId, time, location, activity, notes, latitude || null, longitude || null, order_index || 0).run();

    return c.json({ id: result.meta.last_row_id, daily_schedule_id: scheduleId, ...body }, 201);
  } catch (error) {
    return c.json({ error: 'Failed to add schedule item' }, 500);
  }
});

export default app;