-- 城市表
CREATE TABLE IF NOT EXISTS cities (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  sort_order INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 區域表
CREATE TABLE IF NOT EXISTS districts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  city_id INTEGER NOT NULL,
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  postal_code TEXT,
  latitude REAL,
  longitude REAL,
  sort_order INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (city_id) REFERENCES cities(id) ON DELETE CASCADE
);

-- 行程表（更新為使用 city_id 和 district_id）
CREATE TABLE IF NOT EXISTS itineraries (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  city_id INTEGER,
  district_id INTEGER,
  title TEXT NOT NULL,
  description TEXT,
  start_date DATE,
  end_date DATE,
  day_number INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (city_id) REFERENCES cities(id) ON DELETE SET NULL,
  FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE SET NULL
);

-- 行程項目表（加入地點座標）
CREATE TABLE IF NOT EXISTS itinerary_items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  itinerary_id INTEGER NOT NULL,
  time TEXT,
  location TEXT NOT NULL,
  activity TEXT NOT NULL,
  notes TEXT,
  latitude REAL,
  longitude REAL,
  order_index INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (itinerary_id) REFERENCES itineraries(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_cities_code ON cities(code);
CREATE INDEX idx_cities_name ON cities(name);
CREATE INDEX idx_districts_city_id ON districts(city_id);
CREATE INDEX idx_districts_code ON districts(code);
CREATE INDEX idx_districts_name ON districts(name);
CREATE INDEX idx_districts_postal_code ON districts(postal_code);
CREATE INDEX idx_itineraries_city ON itineraries(city_id);
CREATE INDEX idx_itineraries_district ON itineraries(district_id);
CREATE INDEX idx_itinerary_items_itinerary ON itinerary_items(itinerary_id);
CREATE INDEX idx_itinerary_items_order ON itinerary_items(order_index);