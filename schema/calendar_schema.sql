-- 重新設計：以日期為主體的行程管理系統

-- 城市表（保持不變）
CREATE TABLE IF NOT EXISTS cities (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  sort_order INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 區域表（保持不變）
CREATE TABLE IF NOT EXISTS districts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  city_id INTEGER NOT NULL,
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  postal_code TEXT,
  latitude REAL,
  longitude REAL,
  sort_order INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (city_id) REFERENCES cities(id) ON DELETE CASCADE
);

-- 日程表（以日期為主體）
CREATE TABLE IF NOT EXISTS daily_schedules (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  date DATE NOT NULL,  -- 日期（主鍵之一）
  city_id INTEGER,     -- 該日主要城市
  district_id INTEGER, -- 該日主要區域
  title TEXT,          -- 該日行程標題
  notes TEXT,          -- 該日總體備註
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (city_id) REFERENCES cities(id) ON DELETE SET NULL,
  FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE SET NULL
);

-- 日程項目表（一天內的具體行程）
CREATE TABLE IF NOT EXISTS schedule_items (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  daily_schedule_id INTEGER NOT NULL,
  time TEXT,            -- 時間（如 "09:00", "14:30"）
  location TEXT NOT NULL, -- 地點
  activity TEXT NOT NULL, -- 活動內容
  notes TEXT,           -- 備註
  latitude REAL,        -- 經度
  longitude REAL,       -- 緯度
  order_index INTEGER DEFAULT 0, -- 排序
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (daily_schedule_id) REFERENCES daily_schedules(id) ON DELETE CASCADE
);

-- 索引優化（以日期查詢為主）
CREATE INDEX idx_daily_schedules_date ON daily_schedules(date);
CREATE INDEX idx_daily_schedules_city ON daily_schedules(city_id);
CREATE INDEX idx_daily_schedules_district ON daily_schedules(district_id);
CREATE INDEX idx_schedule_items_daily ON schedule_items(daily_schedule_id);
CREATE INDEX idx_schedule_items_order ON schedule_items(order_index);

-- 刪除舊表（如果需要的話）
-- DROP TABLE IF EXISTS itineraries;
-- DROP TABLE IF EXISTS itinerary_items;