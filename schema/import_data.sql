-- 匯入台灣城市和區域資料
-- 先插入城市資料
INSERT OR IGNORE INTO cities (code, name, sort_order) VALUES
  ('TPE', '台北市', 1),
  ('NTPC', '新北市', 2),
  ('KEE', '基隆市', 3),
  ('ILA', '宜蘭縣', 4),
  ('HSC', '新竹市', 5),
  ('HSQ', '新竹縣', 6),
  ('TYC', '桃園市', 7),
  ('MIA', '苗栗縣', 8),
  ('TXG', '台中市', 9),
  ('CHA', '彰化縣', 10),
  ('NAN', '南投縣', 11),
  ('CYI', '嘉義市', 12),
  ('CYQ', '嘉義縣', 13),
  ('YUN', '雲林縣', 14),
  ('TNN', '台南市', 15),
  ('KHH', '高雄市', 16),
  ('PIF', '屏東縣', 17),
  ('TTT', '台東縣', 18),
  ('HUA', '花蓮縣', 19),
  ('PEN', '澎湖縣', 20),
  ('KMN', '金門縣', 21),
  ('LJF', '連江縣', 22);

-- 插入區域資料 (從 CSV 資料轉換)
-- 台北市
INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)
SELECT c.id, '100', '中正區', '100', 25.0324, 121.5187 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '103', '大同區', '103', 25.0663, 121.5134 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '104', '中山區', '104', 25.0690, 121.5336 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '105', '松山區', '105', 25.0491, 121.5774 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '106', '大安區', '106', 25.0266, 121.5431 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '108', '萬華區', '108', 25.0324, 121.4982 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '110', '信義區', '110', 25.0310, 121.5717 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '111', '士林區', '111', 25.0928, 121.5243 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '112', '北投區', '112', 25.1325, 121.5014 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '114', '內湖區', '114', 25.0696, 121.5912 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '115', '南港區', '115', 25.0540, 121.6063 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '116', '文山區', '116', 24.9899, 121.5729 FROM cities c WHERE c.code = 'TPE';

-- 新北市
INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)
SELECT c.id, '207', '萬里區', '207', 25.1807, 121.6879 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '208', '金山區', '208', 25.2222, 121.6359 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '220', '板橋區', '220', 25.0116, 121.4544 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '221', '汐止區', '221', 25.0628, 121.6404 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '222', '深坑區', '222', 25.0021, 121.6158 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '223', '石碇區', '223', 24.9914, 121.6580 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '224', '瑞芳區', '224', 25.1090, 121.8100 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '226', '平溪區', '226', 25.0256, 121.7383 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '227', '雙溪區', '227', 25.0336, 121.8656 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '228', '貢寮區', '228', 25.0218, 121.9085 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '231', '新店區', '231', 24.9674, 121.5415 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '232', '坪林區', '232', 24.9388, 121.7116 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '233', '烏來區', '233', 24.8654, 121.5508 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '234', '永和區', '234', 25.0074, 121.5138 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '235', '中和區', '235', 24.9992, 121.4935 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '236', '土城區', '236', 24.9724, 121.4433 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '237', '三峽區', '237', 24.9344, 121.3684 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '238', '樹林區', '238', 24.9908, 121.4245 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '239', '鶯歌區', '239', 24.9548, 121.3546 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '241', '三重區', '241', 25.0616, 121.4881 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '242', '新莊區', '242', 25.0360, 121.4506 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '243', '泰山區', '243', 25.0590, 121.4309 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '244', '林口區', '244', 25.0772, 121.3917 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '247', '蘆洲區', '247', 25.0849, 121.4733 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '248', '五股區', '248', 25.0825, 121.4381 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '249', '八里區', '249', 25.1466, 121.3984 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '251', '淡水區', '251', 25.1697, 121.4408 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '252', '三芝區', '252', 25.2578, 121.5007 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '253', '石門區', '253', 25.2903, 121.5684 FROM cities c WHERE c.code = 'NTPC';

-- 基隆市
INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)
SELECT c.id, '200', '仁愛區', '200', 25.1278, 121.7402 FROM cities c WHERE c.code = 'KEE'
UNION ALL SELECT c.id, '201', '信義區', '201', 25.1293, 121.7519 FROM cities c WHERE c.code = 'KEE'
UNION ALL SELECT c.id, '202', '中正區', '202', 25.1416, 121.7742 FROM cities c WHERE c.code = 'KEE'
UNION ALL SELECT c.id, '203', '中山區', '203', 25.1565, 121.7373 FROM cities c WHERE c.code = 'KEE'
UNION ALL SELECT c.id, '204', '安樂區', '204', 25.1208, 121.7227 FROM cities c WHERE c.code = 'KEE'
UNION ALL SELECT c.id, '205', '暖暖區', '205', 25.0991, 121.7365 FROM cities c WHERE c.code = 'KEE'
UNION ALL SELECT c.id, '206', '七堵區', '206', 25.0957, 121.7130 FROM cities c WHERE c.code = 'KEE';

-- 宜蘭縣
INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)
SELECT c.id, '260', '宜蘭市', '260', 24.7522, 121.7535 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '261', '頭城鎮', '261', 24.8593, 121.8232 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '262', '礁溪鄉', '262', 24.8268, 121.7707 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '263', '壯圍鄉', '263', 24.7448, 121.7816 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '264', '員山鄉', '264', 24.7418, 121.7219 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '265', '羅東鎮', '265', 24.6769, 121.7667 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '266', '三星鄉', '266', 24.6643, 121.6515 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '267', '大同鄉', '267', 24.5749, 121.5060 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '268', '五結鄉', '268', 24.6844, 121.7984 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '269', '冬山鄉', '269', 24.6343, 121.7919 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '270', '蘇澳鎮', '270', 24.5946, 121.8517 FROM cities c WHERE c.code = 'ILA'
UNION ALL SELECT c.id, '272', '南澳鄉', '272', 24.4636, 121.8004 FROM cities c WHERE c.code = 'ILA';

-- 這裡只先匯入北部主要縣市，如需要所有縣市資料，可以繼續增加其他縣市...
-- 可以使用腳本自動產生剩餘的所有縣市區域