-- 匯入主要縣市的區域資料（範例）
-- 台北市
INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)
SELECT c.id, '100', '中正區', '100', 25.0324, 121.5187 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '103', '大同區', '103', 25.0663, 121.5134 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '104', '中山區', '104', 25.0690, 121.5336 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '105', '松山區', '105', 25.0491, 121.5774 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '106', '大安區', '106', 25.0266, 121.5431 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '108', '萬華區', '108', 25.0324, 121.4982 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '110', '信義區', '110', 25.0310, 121.5717 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '111', '士林區', '111', 25.0928, 121.5243 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '112', '北投區', '112', 25.1325, 121.5014 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '114', '內湖區', '114', 25.0696, 121.5912 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '115', '南港區', '115', 25.0540, 121.6063 FROM cities c WHERE c.code = 'TPE'
UNION ALL SELECT c.id, '116', '文山區', '116', 24.9899, 121.5729 FROM cities c WHERE c.code = 'TPE';

-- 新北市（部分區域）
INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)
SELECT c.id, '220', '板橋區', '220', 25.0116, 121.4544 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '234', '永和區', '234', 25.0074, 121.5138 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '235', '中和區', '235', 24.9992, 121.4935 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '241', '三重區', '241', 25.0616, 121.4881 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '242', '新莊區', '242', 25.0360, 121.4506 FROM cities c WHERE c.code = 'NTPC'
UNION ALL SELECT c.id, '251', '淡水區', '251', 25.1697, 121.4408 FROM cities c WHERE c.code = 'NTPC';

-- 台中市（部分區域）
INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)
SELECT c.id, '400', '中區', '400', 24.1439, 120.6797 FROM cities c WHERE c.code = 'TXG'
UNION ALL SELECT c.id, '401', '東區', '401', 24.1366, 120.7041 FROM cities c WHERE c.code = 'TXG'
UNION ALL SELECT c.id, '402', '南區', '402', 24.1215, 120.6643 FROM cities c WHERE c.code = 'TXG'
UNION ALL SELECT c.id, '403', '西區', '403', 24.1416, 120.6710 FROM cities c WHERE c.code = 'TXG'
UNION ALL SELECT c.id, '404', '北區', '404', 24.1663, 120.6823 FROM cities c WHERE c.code = 'TXG'
UNION ALL SELECT c.id, '406', '北屯區', '406', 24.1823, 120.6858 FROM cities c WHERE c.code = 'TXG';

-- 台南市（部分區域）
INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)
SELECT c.id, '700', '中西區', '700', 22.9918, 120.2058 FROM cities c WHERE c.code = 'TNN'
UNION ALL SELECT c.id, '701', '東區', '701', 22.9801, 120.2240 FROM cities c WHERE c.code = 'TNN'
UNION ALL SELECT c.id, '702', '南區', '702', 22.9609, 120.1886 FROM cities c WHERE c.code = 'TNN'
UNION ALL SELECT c.id, '704', '北區', '704', 23.0098, 120.2072 FROM cities c WHERE c.code = 'TNN'
UNION ALL SELECT c.id, '708', '安平區', '708', 23.0005, 120.1659 FROM cities c WHERE c.code = 'TNN'
UNION ALL SELECT c.id, '710', '永康區', '710', 23.0262, 120.2572 FROM cities c WHERE c.code = 'TNN';

-- 高雄市（部分區域）
INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)
SELECT c.id, '800', '新興區', '800', 22.6310, 120.3091 FROM cities c WHERE c.code = 'KHH'
UNION ALL SELECT c.id, '801', '前金區', '801', 22.6275, 120.2943 FROM cities c WHERE c.code = 'KHH'
UNION ALL SELECT c.id, '802', '苓雅區', '802', 22.6216, 120.3120 FROM cities c WHERE c.code = 'KHH'
UNION ALL SELECT c.id, '807', '三民區', '807', 22.6477, 120.2998 FROM cities c WHERE c.code = 'KHH'
UNION ALL SELECT c.id, '813', '左營區', '813', 22.6901, 120.2948 FROM cities c WHERE c.code = 'KHH'
UNION ALL SELECT c.id, '830', '鳳山區', '830', 22.6272, 120.3580 FROM cities c WHERE c.code = 'KHH';