# 地區行程管理系統

使用 Cloudflare Workers 和 D1 資料庫建立的地區分類行程管理系統。

## 功能

- 🌏 按地區分類管理行程
- 📅 新增、編輯、刪除行程
- 📍 詳細行程項目管理
- 📦 使用 D1 資料庫儲存資料
- ⚡ Cloudflare Workers 提供快速響應

## 安裝步驟

### 1. 安裝依賴
```bash
npm install
```

### 2. 建立 D1 資料庫
```bash
npx wrangler d1 create booking-db
```

將返回的 database_id 更新到 `wrangler.toml` 中的 `database_id` 欄位。

### 3. 初始化資料庫
```bash
npx wrangler d1 execute booking-db --local --file=./schema/schema.sql
```

生產環境：
```bash
npx wrangler d1 execute booking-db --remote --file=./schema/schema.sql
```

### 4. 本地開發
```bash
npm run dev
```

### 5. 部署到 Cloudflare
```bash
npm run deploy
```

## API 端點

- `GET /` - 主頁面
- `GET /api/regions` - 取得所有地區
- `GET /api/regions/:id/itineraries` - 取得特定地區的行程
- `GET /api/itineraries/:id` - 取得行程詳情
- `POST /api/itineraries` - 新增行程
- `POST /api/itineraries/:id/items` - 新增行程項目
- `PUT /api/itineraries/:id` - 更新行程
- `DELETE /api/itineraries/:id` - 刪除行程

## 資料庫結構

### regions 表
- `id` - 主鍵
- `name` - 地區名稱
- `description` - 地區描述

### itineraries 表
- `id` - 主鍵
- `region_id` - 地區 ID
- `title` - 行程標題
- `description` - 行程描述
- `start_date` - 開始日期
- `end_date` - 結束日期
- `day_number` - 天數

### itinerary_items 表
- `id` - 主鍵
- `itinerary_id` - 行程 ID
- `time` - 時間
- `location` - 地點
- `activity` - 活動
- `notes` - 備註
- `order_index` - 排序