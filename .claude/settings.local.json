{"permissions": {"allow": ["Bash(npm create:*)", "Bash(npm install)", "Bash(find:*)", "Bash(node:*)", "Bash(npx wrangler d1 create:*)", "Bash(npm install:*)", "<PERSON>sh(npx wrangler auth:*)", "<PERSON><PERSON>(npx wrangler:*)", "Bash(npm run dev:*)", "WebFetch(domain:localhost)", "<PERSON><PERSON>(curl:*)", "Bash(npm run deploy:*)", "WebFetch(domain:booking-app.arguskao.workers.dev)", "<PERSON><PERSON>(python3:*)", "Bash(sqlite3:*)", "Bash(lsof:*)", "<PERSON><PERSON>(pkill:*)", "Bash(while read id)", "Bash(for:*)", "Bash(done)"], "deny": [], "ask": []}}