-- 創建城市表
CREATE TABLE IF NOT EXISTS cities (
    id SERIAL PRIMARY KEY,
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(20) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 創建區域表
CREATE TABLE IF NOT EXISTS districts (
    id SERIAL PRIMARY KEY,
    city_id INTEGER REFERENCES cities(id) ON DELETE CASCADE,
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(20) NOT NULL,
    postal_code VARCHAR(10),
    latitude DECIMAL(10, 6),
    longitude DECIMAL(10, 6),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 創建索引
CREATE INDEX idx_cities_code ON cities(code);
CREATE INDEX idx_cities_name ON cities(name);
CREATE INDEX idx_districts_city_id ON districts(city_id);
CREATE INDEX idx_districts_code ON districts(code);
CREATE INDEX idx_districts_name ON districts(name);
CREATE INDEX idx_districts_postal_code ON districts(postal_code);

-- 為現有表格添加外鍵欄位（如果需要）
-- ALTER TABLE stores
-- ADD COLUMN IF NOT EXISTS city_id INTEGER REFERENCES cities(id),
-- ADD COLUMN IF NOT EXISTS district_id INTEGER REFERENCES districts(id);

-- ALTER TABLE users
-- ADD COLUMN IF NOT EXISTS city_id INTEGER REFERENCES cities(id),
-- ADD COLUMN IF NOT EXISTS district_id INTEGER REFERENCES districts(id);

-- 建立視圖方便查詢
CREATE OR REPLACE VIEW v_districts_full AS
SELECT
    d.id AS district_id,
    d.code AS district_code,
    d.name AS district_name,
    d.postal_code,
    d.latitude,
    d.longitude,
    c.id AS city_id,
    c.code AS city_code,
    c.name AS city_name,
    CONCAT(c.name, d.name) AS full_name
FROM districts d
JOIN cities c ON d.city_id = c.id
ORDER BY c.sort_order, d.sort_order;