// 從 CSV 檔案產生完整的資料匯入 SQL 腳本
const fs = require('fs');
const path = require('path');

// 讀取 CSV 檔案
const csvPath = path.join(__dirname, '../taiwan_districts.csv');
const csvContent = fs.readFileSync(csvPath, 'utf-8');

// 解析 CSV
const lines = csvContent.split('\n').filter(line => line.trim() && !line.startsWith('city_code'));
const districts = lines.map(line => {
  const [cityCode, cityName, districtCode, districtName, postalCode, latitude, longitude] = line.split(',');
  return {
    cityCode: cityCode?.trim(),
    cityName: cityName?.trim(),
    districtCode: districtCode?.trim(),
    districtName: districtName?.trim(),
    postalCode: postalCode?.trim(),
    latitude: parseFloat(latitude?.trim()),
    longitude: parseFloat(longitude?.trim())
  };
}).filter(d => d.cityCode); // 過濾掉空行

// 取得所有唯一的城市
const uniqueCities = [...new Map(districts.map(d => [d.cityCode, d])).values()];

// 生成 SQL 腳本
let sql = `-- 完整的台灣城市和區域資料匯入腳本\n-- 自動從 taiwan_districts.csv 生成\n\n`;

// 生成城市插入語句
sql += `-- 插入所有城市\n`;
sql += `INSERT OR IGNORE INTO cities (code, name, sort_order) VALUES\n`;

const cityInserts = uniqueCities.map((city, index) => {
  return `  ('${city.cityCode}', '${city.cityName}', ${index + 1})`;
});

sql += cityInserts.join(',\n') + ';\n\n';

// 依城市分組區域
const districtsByCity = districts.reduce((acc, district) => {
  if (!acc[district.cityCode]) {
    acc[district.cityCode] = [];
  }
  acc[district.cityCode].push(district);
  return acc;
}, {});

// 生成每個城市的區域插入語句
Object.entries(districtsByCity).forEach(([cityCode, cityDistricts]) => {
  const cityName = cityDistricts[0].cityName;
  sql += `-- ${cityName} (${cityCode})\n`;
  sql += `INSERT OR IGNORE INTO districts (city_id, code, name, postal_code, latitude, longitude)\n`;

  const districtInserts = cityDistricts.map(district => {
    const lat = isNaN(district.latitude) ? 'NULL' : district.latitude;
    const lng = isNaN(district.longitude) ? 'NULL' : district.longitude;
    return `SELECT c.id, '${district.districtCode}', '${district.districtName}', '${district.postalCode}', ${lat}, ${lng} FROM cities c WHERE c.code = '${cityCode}'`;
  });

  sql += districtInserts.join('\nUNION ALL ') + ';\n\n';
});

// 寫入檔案
const outputPath = path.join(__dirname, '../schema/import_taiwan_data.sql');
fs.writeFileSync(outputPath, sql);

console.log(`✅ 已生成完整的資料匯入腳本: ${outputPath}`);
console.log(`📊 統計資料:`);
console.log(`   - 城市數量: ${uniqueCities.length}`);
console.log(`   - 區域數量: ${districts.length}`);

// 顯示各城市的區域數量
console.log(`\n🏙️ 各城市區域數量:`);
Object.entries(districtsByCity).forEach(([cityCode, cityDistricts]) => {
  console.log(`   ${cityDistricts[0].cityName} (${cityCode}): ${cityDistricts.length} 個區域`);
});